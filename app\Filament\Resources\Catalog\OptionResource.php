<?php

namespace Tasawk\Filament\Resources\Catalog;

use Filament\Notifications\Notification;
use Filament\Tables\Actions\Action;
use Filament\Tables\Columns\IconColumn;
use Filament\Tables\Filters\SelectFilter;
use Illuminate\Database\Eloquent\Model;
use Tasawk\Enum\ModelStatus;
use Tasawk\Filament\Resources\Catalog\OptionResource\Pages;
use Tasawk\Models\Branch;
use Tasawk\Models\Catalog\Option;
use Tasawk\Models\Catalog\Product;
use Tasawk\Traits\Filament\HasTranslationLabel;
use Filament\Forms\Form;
use Filament\Resources\Concerns\Translatable;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;


class OptionResource extends Resource {
    use Translatable;
    use HasTranslationLabel;

    protected static ?string $model = Option::class;
    protected static ?int $navigationSort = 2;
    protected static ?string $navigationIcon = 'heroicon-o-clipboard';


    public static function form(Form $form): Form {
        return $form
            ->schema([


            ]);
    }


    public static function table(Table $table): Table {
        return $table
            ->columns([
                TextColumn::make('id'),
                TextColumn::make('name'),
                IconColumn::make('status')
                    ->boolean()
                    ->action(
                        Action::make('status')
                            ->label(fn(Option $record): string => $record->status ? __('panel.messages.deactivate') : __('panel.messages.activate'))
                            ->disabled(fn(Model $record): bool => !auth()->user()->can('update', $record))
                            ->requiresConfirmation()
                            ->action(fn(Option $record) => $record->toggleStatus())

                    ),
            ])
            ->filters([
                SelectFilter::make('status')
                    ->options(ModelStatus::class)
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make()
//                    ->before(function (Tables\Actions\DeleteAction $action, Option $option) {
//                        if ($option->inventoryOptions()->count()) {
//                            Notification::make()
//                                ->warning()
//                                ->title(__('panel.messages.warning'))
//                                ->body(__('panel.messages.option_belong_to_many_branches', ['option' => $option->name]))
//                                ->persistent()
//                                ->send();
//                            $action->cancel();
//                        }
//                        if ($option->products()->count()) {
//                            Notification::make()
//                                ->warning()
//                                ->title(__('panel.messages.warning'))
//                                ->body(__('panel.messages.option_belong_to_many_products', ['option' => $option->name]))
//                                ->persistent()
//                                ->send();
//                            $action->cancel();
//                        }
//                    }),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])
//            ->checkIfRecordIsSelectableUsing(fn(Model $record): bool => !$record->inventoryOptions()->count() && !$record->products()->count())
            ->emptyStateActions([
                Tables\Actions\CreateAction::make(),
            ]);
    }

    public static function getRelations(): array {
        return [
//            RelationManagers\ValuesRelationManager::class
        ];
    }


    public static function getPages(): array {
        return [
            'index' => Pages\ListOptions::route('/'),
            'create' => Pages\CreateOption::route('/create'),
            'edit' => Pages\EditOption::route('/{record}/edit'),
        ];
    }

    public static function getNavigationGroup(): ?string {
        return __('menu.catalog');
    }
}
