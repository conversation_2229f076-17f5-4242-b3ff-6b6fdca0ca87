<?php

namespace Tasawk\Models;

use Cknow\Money\Casts\MoneyDecimalCast;
use Darryldecode\Cart\CartCondition;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Tasawk\Enum\AllOrderStatus;
use Tasawk\Enum\OrderPaymentStatus;
use Tasawk\Enum\OrderStatus;
use Tasawk\Enum\ReceiptMethods;
use Tasawk\Lib\ArrayStorage;
use Tasawk\Lib\Cart as CoreCart;
use Tasawk\Lib\Utils;
use Tasawk\Models\Order\Condition;
use Tasawk\Models\Order\ItemsLine;
use Tasawk\Notifications\Order\OrderAcceptedNotification;
use Tasawk\Notifications\Order\OrderStatusChangedNotification;

class Order extends Model {

    protected $fillable = [

        'branch_id',
        'customer_id',
        'address_id',
        'date',
        'receipt_method',
        'payment_status',
        'payment_data',
        'status',
        'total',
        'notes'
    ];
    protected $casts = [
        'total' => MoneyDecimalCast::class,
        'date' => 'datetime',
        'payment_data' => 'array',
        'status' => OrderStatus::class,
        'payment_status' => OrderPaymentStatus::class,
        'receipt_method' => ReceiptMethods::class
    ];

    protected static function booted(): void {
        parent::booted(); // TODO: Change the autogenerated stub
        static::updating(function (Order $order) {
            if ($order->getOriginal('status') != $order->status) {
                if ($order->status === OrderStatus::PENDING_FOR_CUSTOMER_PAY->value) {
                    \Notification::send($order->customer, new OrderAcceptedNotification($order));
                }
                if (!in_array($order->status, [OrderStatus::PENDING_FOR_CUSTOMER_PAY->value, OrderStatus::CANCELED->value])) {
                    \Notification::send([$order->customer, ... Utils::getAdministrationUsers()], new OrderStatusChangedNotification($order));
                };
            }
        });
        static::addGlobalScope('manager', function ($builder) {
            if (auth()->user()?->isManager()) {
                $builder->where('branch_id', auth()->user()->manager->mainBranch()?->id);
            }
            if (auth()->user()?->isOperator()) {
                $builder->where('branch_id', auth()->user()->operator->mainBranch()?->id);
            }
        });
    }

    public function scopeToday($builder) {
        $range = now()->isBetween(today()->setTimeFromTimeString("00:00"), today()->setTimeFromTimeString("03:00"))

            ? [today()->subDay()->setTimeFromTimeString("07:00"), today()->setTimeFromTimeString("03:00")]
            : [today()->setTimeFromTimeString("07:00"), today()->addDay()->setTimeFromTimeString("03:00")];
        return $builder->whereBetween('date', $range);
    }

    public function getOrderNumberAttribute(): string {
        return sprintf("%'.06d", $this->id);
    }

    function itemsLine(): HasMany {
        return $this->hasMany(ItemsLine::class);
    }

    function conditions(): HasMany {
        return $this->hasMany(Condition::class);
    }

    public function getAsCartAttribute() {
        $eventsClass = config('shopping_cart.events');
        $events = $eventsClass ? new $eventsClass() : app('events');
        $session_key = md5($this->cart_id . \Str::random());
        $instanceName = $session_key . 'back_end_order_cart';
        $cart = new CoreCart(
            new ArrayStorage,
            $events,
            $instanceName,
            $session_key,
            config('shopping_cart')
        );

        $this->itemsLine->transform(function (ItemsLine $item) {

            $conditions = collect($item->conditions)->map(fn($cond) => new CartCondition($cond))->toArray();
            $item['quantity'] = $item->quantity > 0 ? $item->quantity : 1;
            $item['associatedModel'] = (object)$item->model;
            $item['new_conditions'] = $conditions;
            return $item;
        })->each(function ($item) use ($cart) {
            $d = $item->toArray();
            $d['conditions'] = $d['new_conditions'];
            return $cart->add($d);
        });
        $this->conditions->each(fn($condition) => $cart->condition(new CartCondition($condition->toArray())));

        return $cart;
    }

    public function address(): BelongsTo {
        return $this->belongsTo(AddressBook::class);
    }

    public function customer() {
        return $this->belongsTo(Customer::class);
    }

    public function rate() {
        return $this->hasOne(OrderRate::class);
    }

    public function rated() {
        return $this->rate()->exists();
    }

    public function branch() {
        return $this->belongsTo(Branch::class);
    }

    public function cancellation(): HasOne {
        return $this->hasOne(OrderCancellation::class, "order_id");
    }

    public function cancel($reason) {
        $this->update(['status' => OrderStatus::CANCELED]);
        return $this->cancellation()->create(['cancellation_reason_id' => $reason]);
    }

    public function getAvailableStatus() {
        $statuses = collect(OrderStatus::cases())->map(fn($status) => $status->value);
        $currentStatusIndex = $statuses->search($this->status->value);
        $standardStatus = collect($statuses)->slice($currentStatusIndex + 1)
            ->filter(fn($status) => $status != OrderStatus::CANCELED->value)
            ->map(function ($status) {
                $statusLabel = $status;
                if ($status == OrderStatus::DELIVERED->value && $this->receipt_method != 'delivery') {

                    $statusLabel = 'received';
                }

                return [
                    'value' => $status,
                    'label' => __("panel.enums." . $statusLabel)
                ];
            })
            ->values();
        if (in_array($this->status->value, [OrderStatus::PENDING_FOR_ACCEPT_ORDER->value, OrderPaymentStatus::PENDING->value]) && $this->status->value !== OrderStatus::CANCELED) {
            $canceledStatuses = CancellationReason::enabled()->get();
            foreach ($canceledStatuses as $canceledStatus) {
                $standardStatus->push([
                    'value' => OrderStatus::CANCELED->value . "-" . $canceledStatus->id,
                    'label' => __("panel.enums." . OrderStatus::CANCELED->value) . " - " . $canceledStatus->name
                ]);
            }
        }
        return $standardStatus
            ->when($this->receipt_method->value !== 'delivery', fn($collection) => $collection->where('value', '!=', OrderStatus::ON_WAY->value))
            ->values();
    }

    public function canRate(): bool {
        return !$this->rate()->exists() && $this->status == OrderStatus::DELIVERED;
    }

    public function getRenderStatus() {

        if ($this->status->value == OrderStatus::DELIVERED->value && $this->receipt_method->value != 'delivery') {
            return __("panel.enums.pickup.delivered");
        }
        if ($this->status == OrderStatus::CANCELED) {
            return $this->status->getLabel() . ' - ' . $this->cancellation?->getReason();
        }
        return $this->status->getLabel();
    }

}
