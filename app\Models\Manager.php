<?php

namespace Tasawk\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;
use Tasawk\Models\Catalog\Branch\BranchAccount;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Laravel\Sanctum\HasApiTokens;
use Spatie\Permission\Traits\HasRoles;

class Manager extends User {
    protected $table = "users";
    protected string $guard_name = 'web';

    public function getMorphClass(): string {
        return User::class;
    }

    protected static function booted() {
        parent::booted();
        static::creating(fn($model) => $model->assignRole('manager'));
        static::addGlobalScope("manager", function ($builder) {
            $builder->whereHas("roles", fn($q) => $q->where('name', 'manager'));
        });
    }

    public function branch() {
        return $this->belongsToMany(Branch::class, 'branch_manager', 'manager_id','branch_id');
    }

    public function mainBranch() {
        return $this->branch->first();
    }

    public function toggleMaintenanceMode() {
        $this->mainBranch()->update(['maintenance_mode' => !$this->mainBranch()->maintenance_mode]);
    }

    public function toggleHeavyLoadMode() {
        $this->mainBranch()->update(['heavy_load_mode' => !$this->mainBranch()->heavy_load_mode]);
    }

}
