<?php

namespace Tasawk\Models\Catalog\Branch;

use Carbon\Carbon;
use Notification;
use Tasawk\Lib\Utils;
use Tasawk\Models\Branch;
use Tasawk\Models\Catalog\Product;
use Tasawk\Notifications\Branch\BranchMaintenanceModeChangedNotification;
use Tasawk\Notifications\Branch\BranchProductStatusChangedNotification;
use Tasawk\Traits\Publishable;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

/**
 * @method enabledOptions()
 * */
class Inventory extends Model {
    use Publishable;

    protected $fillable = [
        'branch_id',
        'product_id',
        'from',
        'to',
        'status',
    ];

    protected static function booted() {
        parent::booted(); // TODO: Change the autogenerated stub
        static::updated(function ($inventory) {
            if ($inventory->getOriginal('status') != $inventory->status) {

                Notification::send(Utils::getAdministrationUsers(), new BranchProductStatusChangedNotification($inventory->branch, $inventory));
            }
        });
    }

    public function scopeFiltered($builder) {
        return $builder->whereHas('product', function ($builder) {
            $builder->filtered();
        });
    }

    public function product(): BelongsTo {
        return $this->belongsTo(Product::class);
    }

    public function options(): HasMany {
        return $this->hasMany(InventoryOption::class);
    }

    public function branch(): BelongsTo {
        return $this->belongsTo(Branch::class);
    }

    public function scopeEnabledOptions() {
        return $this->options()->enabled();
    }

    public function isAvailable(): bool {
        if (!$this->status) {
            return false;
        }
        return !($this->from && $this->to) || now()->isBetween(today()->setTimeFromTimeString($this->from), today()->setTimeFromTimeString($this->to));
    }
}
