[08-Oct-2024 09:26:28 UTC] PHP Fatal error:  Composer detected issues in your platform: Your Composer dependencies require a PHP version ">= 8.1.0". You are running 7.4.33. in /home/<USER>/public_html/cms/vendor/composer/platform_check.php on line 28
[08-Oct-2024 09:26:29 UTC] PHP Fatal error:  Composer detected issues in your platform: Your Composer dependencies require a PHP version ">= 8.1.0". You are running 7.4.33. in /home/<USER>/public_html/cms/vendor/composer/platform_check.php on line 28
[08-Oct-2024 09:26:33 UTC] PHP Fatal error:  Composer detected issues in your platform: Your Composer dependencies require a PHP version ">= 8.1.0". You are running 7.4.33. in /home/<USER>/public_html/cms/vendor/composer/platform_check.php on line 28
[08-Oct-2024 09:28:56 UTC] PHP Fatal error:  Composer detected issues in your platform: Your Composer dependencies require a PHP version ">= 8.1.0". You are running 8.0.30. in /home/<USER>/public_html/cms/vendor/composer/platform_check.php on line 28
[08-Oct-2024 09:28:57 UTC] PHP Fatal error:  Composer detected issues in your platform: Your Composer dependencies require a PHP version ">= 8.1.0". You are running 8.0.30. in /home/<USER>/public_html/cms/vendor/composer/platform_check.php on line 28
[08-Oct-2024 09:28:57 UTC] PHP Fatal error:  Composer detected issues in your platform: Your Composer dependencies require a PHP version ">= 8.1.0". You are running 8.0.30. in /home/<USER>/public_html/cms/vendor/composer/platform_check.php on line 28
[08-Oct-2024 09:28:57 UTC] PHP Fatal error:  Composer detected issues in your platform: Your Composer dependencies require a PHP version ">= 8.1.0". You are running 8.0.30. in /home/<USER>/public_html/cms/vendor/composer/platform_check.php on line 28
[08-Oct-2024 09:28:57 UTC] PHP Fatal error:  Composer detected issues in your platform: Your Composer dependencies require a PHP version ">= 8.1.0". You are running 8.0.30. in /home/<USER>/public_html/cms/vendor/composer/platform_check.php on line 28
[08-Oct-2024 09:28:57 UTC] PHP Fatal error:  Composer detected issues in your platform: Your Composer dependencies require a PHP version ">= 8.1.0". You are running 8.0.30. in /home/<USER>/public_html/cms/vendor/composer/platform_check.php on line 28
[08-Oct-2024 09:28:58 UTC] PHP Fatal error:  Composer detected issues in your platform: Your Composer dependencies require a PHP version ">= 8.1.0". You are running 8.0.30. in /home/<USER>/public_html/cms/vendor/composer/platform_check.php on line 28
[08-Oct-2024 09:28:58 UTC] PHP Fatal error:  Composer detected issues in your platform: Your Composer dependencies require a PHP version ">= 8.1.0". You are running 8.0.30. in /home/<USER>/public_html/cms/vendor/composer/platform_check.php on line 28
[08-Oct-2024 09:28:58 UTC] PHP Fatal error:  Composer detected issues in your platform: Your Composer dependencies require a PHP version ">= 8.1.0". You are running 8.0.30. in /home/<USER>/public_html/cms/vendor/composer/platform_check.php on line 28
[08-Oct-2024 09:28:58 UTC] PHP Fatal error:  Composer detected issues in your platform: Your Composer dependencies require a PHP version ">= 8.1.0". You are running 8.0.30. in /home/<USER>/public_html/cms/vendor/composer/platform_check.php on line 28
[08-Oct-2024 09:28:59 UTC] PHP Fatal error:  Composer detected issues in your platform: Your Composer dependencies require a PHP version ">= 8.1.0". You are running 8.0.30. in /home/<USER>/public_html/cms/vendor/composer/platform_check.php on line 28
[08-Oct-2024 09:28:59 UTC] PHP Fatal error:  Composer detected issues in your platform: Your Composer dependencies require a PHP version ">= 8.1.0". You are running 8.0.30. in /home/<USER>/public_html/cms/vendor/composer/platform_check.php on line 28
[08-Oct-2024 09:29:04 UTC] PHP Fatal error:  Composer detected issues in your platform: Your Composer dependencies require a PHP version ">= 8.1.0". You are running 8.0.30. in /home/<USER>/public_html/cms/vendor/composer/platform_check.php on line 28
[08-Oct-2024 11:48:56 UTC] PHP Fatal error:  Composer detected issues in your platform: Your Composer dependencies require a PHP version ">= 8.1.0". You are running 8.0.30. in /home/<USER>/public_html/cms/vendor/composer/platform_check.php on line 28
[14-Oct-2024 14:32:46 Africa/Cairo] PHP Fatal error:  Maximum execution time of 300 seconds exceeded in /home/<USER>/public_html/cms/vendor/laravel/framework/src/Illuminate/Database/Eloquent/Concerns/HasAttributes.php on line 438
[09-Mar-2025 12:23:14 UTC] PHP Fatal error:  Composer detected issues in your platform: Your Composer dependencies require a PHP version ">= 8.2.0". You are running 8.1.29. in /home/<USER>/public_html/cms/vendor/composer/platform_check.php on line 28
[09-Mar-2025 12:23:30 UTC] PHP Fatal error:  Composer detected issues in your platform: Your Composer dependencies require a PHP version ">= 8.2.0". You are running 8.1.29. in /home/<USER>/public_html/cms/vendor/composer/platform_check.php on line 28
[09-Mar-2025 12:23:35 UTC] PHP Fatal error:  Composer detected issues in your platform: Your Composer dependencies require a PHP version ">= 8.2.0". You are running 8.1.29. in /home/<USER>/public_html/cms/vendor/composer/platform_check.php on line 28
[09-Mar-2025 12:23:35 UTC] PHP Fatal error:  Composer detected issues in your platform: Your Composer dependencies require a PHP version ">= 8.2.0". You are running 8.1.29. in /home/<USER>/public_html/cms/vendor/composer/platform_check.php on line 28
[09-Mar-2025 12:23:35 UTC] PHP Fatal error:  Composer detected issues in your platform: Your Composer dependencies require a PHP version ">= 8.2.0". You are running 8.1.29. in /home/<USER>/public_html/cms/vendor/composer/platform_check.php on line 28
[09-Mar-2025 12:23:35 UTC] PHP Fatal error:  Composer detected issues in your platform: Your Composer dependencies require a PHP version ">= 8.2.0". You are running 8.1.29. in /home/<USER>/public_html/cms/vendor/composer/platform_check.php on line 28
[09-Mar-2025 12:23:35 UTC] PHP Fatal error:  Composer detected issues in your platform: Your Composer dependencies require a PHP version ">= 8.2.0". You are running 8.1.29. in /home/<USER>/public_html/cms/vendor/composer/platform_check.php on line 28
[09-Mar-2025 12:23:35 UTC] PHP Fatal error:  Composer detected issues in your platform: Your Composer dependencies require a PHP version ">= 8.2.0". You are running 8.1.29. in /home/<USER>/public_html/cms/vendor/composer/platform_check.php on line 28
[09-Mar-2025 12:23:35 UTC] PHP Fatal error:  Composer detected issues in your platform: Your Composer dependencies require a PHP version ">= 8.2.0". You are running 8.1.29. in /home/<USER>/public_html/cms/vendor/composer/platform_check.php on line 28
[09-Mar-2025 12:23:35 UTC] PHP Fatal error:  Composer detected issues in your platform: Your Composer dependencies require a PHP version ">= 8.2.0". You are running 8.1.29. in /home/<USER>/public_html/cms/vendor/composer/platform_check.php on line 28
[09-Mar-2025 12:23:47 UTC] PHP Fatal error:  Composer detected issues in your platform: Your Composer dependencies require a PHP version ">= 8.2.0". You are running 8.1.29. in /home/<USER>/public_html/cms/vendor/composer/platform_check.php on line 28
[09-Mar-2025 12:23:50 UTC] PHP Fatal error:  Composer detected issues in your platform: Your Composer dependencies require a PHP version ">= 8.2.0". You are running 8.1.29. in /home/<USER>/public_html/cms/vendor/composer/platform_check.php on line 28
[09-Mar-2025 12:23:51 UTC] PHP Fatal error:  Composer detected issues in your platform: Your Composer dependencies require a PHP version ">= 8.2.0". You are running 8.1.29. in /home/<USER>/public_html/cms/vendor/composer/platform_check.php on line 28
