<?php

namespace Tasawk\Api\V1\Shared;

use DB;
use Tasawk\Api\Facade\Api;
use Tasawk\Http\Resources\Api\Shared\CategoryResource;
use Tasawk\Lib\Utils;
use Tasawk\Models\Catalog\Category;

class CategoryServices  {
    public function list() {
        //Todo:Fetch categories where has products in current selected branch
        $categories = Category::parent()
            ->when(Utils::getBranchFromRequestHeader(),fn($q)=>$q->whereHas('products.inventories',fn($builder)=>$builder->where('branch_id',Utils::getBranchFromRequestHeader())))
            ->enabled()
            ->orderBy(DB::raw("sort = 0, sort"))
            ->get();
        return Api::isOk(__("List of categories"), CategoryResource::collection($categories));
    }

    public function show(Category $category) {
        return Api::isOk(__("Category information"), new CategoryResource($category));
    }


}
