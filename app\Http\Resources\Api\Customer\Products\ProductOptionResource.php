<?php

namespace Tasawk\Http\Resources\Api\Customer\Products;

use Illuminate\Http\Resources\Json\JsonResource;
use Tasawk\Http\Resources\Api\Customer\Products\AllergenResource;

class ProductOptionResource extends JsonResource {


    public function toArray($request) {
        return [
            'id' => $this->id,
            'title' => $this->option->option->name,
            'type' => $this->option->option->type,
            'required' => $this->option->required,
            'values'=>ProductOptionValueResource::collection(
                $this
                ->enabledValues()
                ->get()
            )
        ];
    }
}
