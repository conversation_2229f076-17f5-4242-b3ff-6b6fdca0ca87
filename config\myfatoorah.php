<?php

return [
    /**
     * API Token Key (string)
     * Accepted value:
     * Live Token: https://myfatoorah.readme.io/docs/live-token
     * Test Token: https://myfatoorah.readme.io/docs/test-token
     */
    'api_key' => env('MYFATOORAH_TEST_MODE', false)
        ? 'rLtt6JWvbUHDDhsZnfpAhpYk4dxYDQkbcPTyGaKp2TYqQgG7FGZ5Th_WD53Oq8Ebz6A53njUoo1w3pjU1D4vs_ZMqFiz_j0urb_BH9Oq9VZoKFoJEDAbRZepGcQanImyYrry7Kt6MnMdgfG5jn4HngWoRdKduNNyP4kzcp3mRv7x00ahkm9LAK7ZRieg7k1PDAnBIOG3EyVSJ5kK4WLMvYr7sCwHbHcu4A5WwelxYK0GMJy37bNAarSJDFQsJ2ZvJjvMDmfWwDVFEVe_5tOomfVNt6bOg9mexbGjMrnHBnKnZR1vQbBtQieDlQepzTZMuQrSuKn-t5XZM7V6fCW7oP-uXGX-sMOajeX65JOf6XVpk29DP6ro8WTAflCDANC193yof8-f5_EYY-3hXhJj7RBXmizDpneEQDSaSz5sFk0sV5qPcARJ9zGG73vuGFyenjPPmtDtXtpx35A-BVcOSBYVIWe9kndG3nclfefjKEuZ3m4jL9Gg1h2JBvmXSMYiZtp9MR5I6pvbvylU_PP5xJFSjVTIz7IQSjcVGO41npnwIxRXNRxFOdIUHn0tjQ-7LwvEcTXyPsHXcMD8WtgBh-wxR8aKX7WPSsT1O8d8reb2aR7K3rkV3K82K_0OgawImEpwSvp9MNKynEAJQS6ZHe_J_l77652xwPNxMRTMASk1ZsJL'
        : env('MYFATOORAH_LIVE_API_KEY', '7RwDImFomKlpTI6oPeoEvh2XJ4k7C0MRgUxdUzAI0ZtceL9pDBMAIN4rR8Ps3QU3OBhespd91XdSCnoKDySGU57F3AmJliDVtQfnv7N4TYhoi3d6G4x2RUOw9lE_2fmwcOHEaJ8WuDyUzB1kpulw0NpYn8VNKk3Get6x8hKF_vWGJhxdAcDxuacea_RR_wSqUgMk6YjxX4bkL_ZyZeh6Z9h2t7PhMnKOojFG-pEAu0Ktr6oz-Q74dQEftm8VPEwWn7-KRIZx8YzcLzZftRxAk1zCsgvXLJrK5yA0B-x37Hr6N_BcYnGAUlKhceyBXdhFuzBFpfpiaalHAnAnOtTRgYm3hTCu-5ENfoyjK_Lj2QCjJAdRlKsbIKBDOX7ZQgNmww27pkVe_SCkXqbxRhlQw2MWo2FUkvnocjqPAnoiUMnDu75p-Bf8CzU4l2d_O3sNo0hYMTQG7n6sSQTpssk5CW43CKpGpvfkLkUyVWh4FT0kGvSeNgKsIZeMmymIZ_dNZ8rRZFhh0DmG1w1nr0Nwr-qz0WIQ2glA5qyOfiFXApWy_B2Popc4Aaoxr7OFrNINI3N39cM40cNg39JWvcbvLjby1nfeSnbW7_ts44MyNTMws5--5ZLfSDtSq2rxgdMi-eIkQqy5ho7xvdhlSDQyrmAOQwqBSOVcpiGAkds4emjarrqrM0muWvOK2exb4CQ2-qllwf3kmV_0LFQA_iBPKcq0UcKMGcpvXFqF-BmYK_M3Uaw-'),
    /**
     * Test Mode (boolean)
     * Accepted value: true for the test mode or false for the live mode
     */
    'test_mode' => env('MYFATOORAH_TEST_MODE', false),
    /**
     * Country ISO Code (string)
     * Accepted value: KWT, SAU, ARE, QAT, BHR, OMN, JOD, or EGY.
     */
    'country_iso' => 'KWT'
];
