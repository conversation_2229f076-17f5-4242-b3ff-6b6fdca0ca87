<?php

namespace Tasawk\Filament\Widgets;

use <PERSON><PERSON><PERSON><PERSON><PERSON>h\FilamentShield\Traits\HasWidgetShield;
use DB;
use Filament\Widgets\ChartWidget;
use Illuminate\Contracts\Support\Htmlable;
use Tasawk\Models\Order;

class OrderReceiptMethods extends ChartWidget {
    use HasWidgetShield;

    protected static ?int $sort = 5;

    protected function getData(): array {
        $stats = DB::table('orders')->select(
            DB::raw("SUM(
		CASE WHEN receipt_method = 'takeaway'
		THEN
			1
		ELSE
			0
		END) AS 'takeaway',
		SUM(
		CASE WHEN receipt_method ='local'
		THEN
			1
		ELSE
			0
		END) AS 'local',
		SUM(
		CASE WHEN receipt_method = 'delivery'
		THEN
			1
		ELSE
			0
		END) AS 'delivery'
"))->first();

        return [
            'datasets' => [
                [
                    'label' => 'Blog posts created',
                    'data' => [$stats->takeaway, $stats->local, $stats->delivery],
                    'backgroundColor' => [
                        '#ff9999',
                        '#66b3ff',
                        '#ffcc99'
                    ],
                ],
            ],
            'labels' => [__('panel.enums.takeaway'), __('panel.enums.local'), __('panel.enums.delivery')],
        ];
    }

    protected function getType(): string {
        return 'doughnut';
    }

    public function getHeading(): string|Htmlable|null {
        return __('panel.stats.order_receipt_methods');
    }

    public function getDescription(): string|Htmlable|null {
        return __('panel.stats.order_receipt_methods_description');
    }
    public function getTableHeading(): ?string {
        return $this->getHeading();
    }
}
