<?php

namespace Tasawk\Filament\Resources\Content;

use Filament\Forms\Components\Section;
use Filament\Forms\Components\SpatieMediaLibraryFileUpload;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Toggle;
use Filament\Forms\Form;
use Filament\Resources\Concerns\Translatable;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Actions\Action;
use Filament\Tables\Columns\IconColumn;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;
use Tasawk\Filament\Resources\Content;
use Tasawk\Models\Content\CustomerReview;
use Tasawk\Traits\Filament\HasTranslationLabel;


class CustomerReviewResource extends Resource {
    use Translatable;
    use HasTranslationLabel;

    protected static ?string $model = CustomerReview::class;

    protected static ?string $navigationIcon = 'heroicon-o-star';

    public static function form(Form $form): Form {
        return $form
            ->schema([
                Section::make("basic_information")
                    ->schema([
                        TextInput::make('customer_name')
                            ->required()
                            ->translateLabel(),

                        Textarea::make('review')
                            ->required()
                            ->translateLabel(),

                        TextInput::make('rate')
                            ->rules(['numeric', 'min:1', 'max:5'])
                            ->required()
                            ->translateLabel(),

                        Toggle::make('status')->default(1)
                            ->onColor('success')
                            ->offColor('danger')

                    ])
            ]);
    }

    public static function table(Table $table): Table {
        return $table
            ->columns([
                TextColumn::make('id'),
                TextColumn::make('customer_name'),
                IconColumn::make('status')
                    ->boolean()
                    ->action(
                        Action::make('Active')
                            ->label(fn(CustomerReview $record): string => $record->status ? __('panel.messages.deactivate') : __('panel.messages.activate'))
//                            ->disabled(fn(Model $record): bool => !filament()->auth()->user()->can('update', $record))
                            ->requiresConfirmation()
                            ->action(fn(CustomerReview $record) => $record->toggleStatus())


                    )
            ])
            ->filters([
                //
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array {
        return [
            //
        ];
    }

    public static function getPages(): array {
        return [
            'index' => Content\CustomerReviewResource\Pages\ListCustomerReviews::route('/'),
            'create' => Content\CustomerReviewResource\Pages\CreateCustomerReview::route('/create'),
            'edit' => Content\CustomerReviewResource\Pages\EditCustomerReview::route('/{record}/edit'),
        ];
    }
    public static function getNavigationGroup(): ?string {
        return __('menu.content');
    }
}
