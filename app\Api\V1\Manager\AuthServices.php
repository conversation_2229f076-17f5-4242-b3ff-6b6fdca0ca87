<?php

namespace Tasawk\Api\V1\Manager;


use Api;
use Auth;
use Tasawk\Actions\Manager\ManagerHasRightsToLogin;
use Tasawk\Actions\Shared\Authentication\ForgetPassword;
use Tasawk\Actions\Shared\Authentication\UpdateUserPassword;
use Tasawk\Actions\Shared\Authentication\UpdateUserToken;
use Tasawk\Api\Core;
use Tasawk\Http\Requests\Api\Manager\Authentication\CodeConfirmRequest;
use Tasawk\Http\Requests\Api\Manager\Authentication\ForgetPasswordRequest;
use Tasawk\Http\Requests\Api\Manager\Authentication\LoginRequest;
use Tasawk\Http\Requests\Api\Manager\Authentication\ResetPasswordRequest;
use Tasawk\Http\Resources\Api\Manager\ManagerResource;


class AuthServices {

    public function login(LoginRequest $request) {
        if (!Auth::once($request->only("phone", 'password'))) {
            return Api::isError(__('validation.api.invalid_credentials'))->setErrors(['credentials' => __('validation.api.invalid_credentials')]);
        }
        ManagerHasRightsToLogin::run();
        UpdateUserToken::run(auth()->user());

        return Api::isOk(__("Manager information"))->setData(new ManagerResource(auth()->user()));
    }


    public function verifySMSCode(CodeConfirmRequest $request): Core {
        return Api::isOk(__("Correct Verification code"));

    }

    public function forgetPassword(ForgetPasswordRequest $request): Core {
        ForgetPassword::run($request->currentUser());
        return Api::isOk(__("SMS code sent"));

    }

    public function resetPassword(ResetPasswordRequest $request): Core {
        UpdateUserPassword::run($request->currentUser(), $request->get('password'));
        return Api::isOk(__("User information"))->setData(new ManagerResource($request->currentUser()));

    }



}
