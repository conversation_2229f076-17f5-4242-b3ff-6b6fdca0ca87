<?php

namespace Tasawk\Models\Catalog\Product;

use Cknow\Money\Casts\MoneyDecimalCast;
use Cknow\Money\Money;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Tasawk\Models\Catalog\Value;
use Tasawk\Traits\Publishable;

class ProductOptionValue extends Model {
    use Publishable;

    protected $table = 'products_options_values';
    protected $fillable = [
        'product_option_id',
        'value_id',
        'status',
        'price'
    ];


    public function option(): BelongsTo {
        return $this->belongsTo(ProductOption::class, 'product_option_id');
    }

    public function value(): BelongsTo {
        return $this->belongsTo(Value::class);
    }

    public function price(): Attribute {
        return Attribute::make(
            get: fn($value) => Money::parse($value)
        );
    }


}
