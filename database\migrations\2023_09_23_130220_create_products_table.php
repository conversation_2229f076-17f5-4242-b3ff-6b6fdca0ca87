<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void {
        Schema::create('products', function (Blueprint $table) {
            $table->id();
            $table->text('title');
            $table->text('description')->nullable();
            $table->boolean('status')->default(0);
            $table->float('price',8,3);
            $table->float('sale_price',8,3);
            $table->json('more_data')->nullable();
            $table->timestamps();
        });
        Schema::create('category_product', function (Blueprint $table) {
            $table->foreignId("product_id")->constrained()->onDelete('cascade');
            $table->foreignId("category_id")->constrained()->onDelete('cascade');
        });
        Schema::create('allergen_product', function (Blueprint $table) {
            $table->foreignId("product_id")->constrained()->onDelete('cascade');
            $table->foreignId("allergen_id")->constrained()->onDelete('cascade');
        });
        Schema::create('option_product', function (Blueprint $table) {
            $table->foreignId("product_id")->constrained()->onDelete('cascade');
            $table->foreignId("option_id")->constrained()->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void {
        Schema::dropIfExists('category_product');
        Schema::dropIfExists('allergen_product');
        Schema::dropIfExists('option_product');
        Schema::dropIfExists('products');
    }
};
