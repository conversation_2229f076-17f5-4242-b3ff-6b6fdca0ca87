<?php

namespace Tasawk\Api\V1\Customer;

use Api;
use Tasawk\AdminPanel\Http\Controllers\Controller;
use Tasawk\Api\Core;
use Tasawk\Ecommerce\Http\Resources\Api\Customer\SliderResource;
use Tasawk\Sliders\Model\Slider;

class HomeController extends Controller {


    public function slider(): Core {
        $sliders = Slider::active()->latest()->get();

        return Api::isOk(__("List of sliders"), SliderResource::collection($sliders));
    }


}
