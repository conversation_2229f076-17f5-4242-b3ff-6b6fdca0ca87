<?php

namespace Tasawk\Actions\Shared\Authentication;


use Lorisleiva\Actions\Concerns\AsAction;
use SMS;
use Utilities;

class ForgetPassword {
    use AsAction;

    public function handle($user) {
        $code = 123456;
        $user->verificationCodes()->create(['phone' => $phone ?? $user->phone, "code" => $code]);
        dispatch(function () use ($user, $code) {
//Todo: uncomment this when you want to send email
//            Notification::send($user, new ForgetPasswordVerficationCodeNotification());
//            Mail::to($user)->send(new ForgetPasswordVerificationCodeMail($code));

//            SMS::to($user->phone)
//                ->message(__("KUFA - use the activation code :CODE to reset your password", ['CODE' => $code]))
//                ->send();
        })->afterResponse();

    }

}
