<?php

namespace Tasawk\Actions\Customer;

use Exception;
use Lorisle<PERSON>\Actions\Concerns\AsAction;
use Tasawk\Models\Order;
use Tasawk\Services\MyFatoorahPaymentService;
use Illuminate\Support\Facades\Log;

class PayOrderAction {
    use AsAction;

    public function handle(Order $order, MyFatoorahPaymentService $paymentService) {

        if (request()->get('payment_gateway') == 'cash') {
            $order->update(['payment_status' => 'pending']);
            $order->update(['payment_data' => ['gateway' => 'cash', 'method' => 'cash']]);

            return;
        }

        try {
            // Prepare payment data for the new service
            $orderData = [
                'customer_name' => $order?->customer?->name ?? '',
                'invoice_value' => $order->total->formatByDecimal(),
                'currency' => 'SAR',
                'customer_email' => $order?->customer?->email,
                'callback_url' => route('webhooks.myfatoorah.callback'),
                'error_url' => route('webhooks.myfatoorah.callback'),
                'mobile_country_code' => '+965',
                'customer_mobile' => $order->customer?->phone,
                'language' => app()->getLocale(),
                'customer_reference' => (string) $order->id,
            ];

            // Format the data according to MyFatoorah API requirements
            $paymentData = $paymentService->formatPaymentData($orderData);

            // Create the invoice
            $invoiceData = $paymentService->createInvoice($paymentData);

            // Update order with payment data
            $order->update([
                'payment_data' => [
                    ...$invoiceData,
                    'gateway' => 'myfatoorah'
                ]
            ]);

        } catch (Exception $e) {
            Log::error('PayOrderAction Error: ' . $e->getMessage(), [
                'order_id' => $order->id,
                'customer_id' => $order->customer_id
            ]);

            // You might want to handle this error differently based on your requirements
            throw $e;
        }
    }

}
