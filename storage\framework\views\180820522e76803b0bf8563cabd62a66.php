<?php $__env->startSection('content'); ?>

    <?php echo $__env->make('site.includes.main-banner', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

    <?php if($landing_page->about_description['active'] == 1): ?>

        <?php echo $__env->make('site.includes.about-us', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

    <?php endif; ?>


    <!-- Start features -->
    <?php if($landing_page->our_features_description['active'] == 1): ?>

        <section class="features" id="features-sec">
            <div class="container">
                <div class="features-parent">
                    <img class="mobile-hand-img" data--aos-duration="1000" data-aos-delay="1500" data-aos="fade-right"
                         data-aos-easing="ease-out-cubic" data-aos-duration="700"
                         src="<?php echo e(url('/storage/' . $landing_page->our_features_image)); ?>" alt="">
                    <div class="features-cont">
                        <div class="titles-parent">
                            <h4 class="sub-title"><?php echo e(trans('site.some_greate_feature')); ?></h4></h4>
                            <h2 class="general-title">
                                <?php echo e($landing_page->our_features_description[app()->getLocale()]??''); ?>

                            </h2>
                        </div>
                        <div class="features-list">
                            <?php $__currentLoopData = $landing_page->features; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $feature): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                <div class="list-item">
                                    <div class="icon-circle">
                                        <i class="fa-regular fa-<?php echo e($feature['icon']??''); ?> icon"></i>
                                    </div>
                                    <h4 class="item-title"> <?php echo e($feature['title'][app()->getLocale()]??''); ?> </h4>
                                </div>

                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

                        </div>
                    </div>
                </div>
            </div>


        </section>

    <?php endif; ?>
    <!-- End features -->



    <!-- Start app-screens -->
    <?php if($landing_page->app_screen['active'] == 1): ?>
        <section class="app-screens" id="appScreens-sec">
            <h3 class="title-text" data-aos="zoom-in-down" data-aos-easing="ease-out-cubic" data-aos-duration="1000">
                <?php echo e(trans('site.images_from_app')); ?></h3>
            <div class="mySwiper-parent">
                <div class="swiper">
                    <div class="swiper-wrapper">
                        <?php $__currentLoopData = $landing_page->app_screen['image']??[]; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $image): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <div class="swiper-slide">

                                <div class="screenShot">
                                    <img src="<?php echo e(url('/storage/' . $image['image'])); ?>" alt="">
                                </div>
                            </div>

                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>


                    </div>
                </div>
                <div class="swiper-pagination"></div>
                <div class="frame">
                    <img src="images/frame.png" alt="">
                </div>

            </div>

        </section>
    <?php endif; ?>
    <?php echo $__env->make("site.includes.clients-reviews", \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
    <?php echo $__env->make("site.includes.faqs", \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

    <?php echo $__env->make('site.includes.apps', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

    <?php echo $__env->make('site.includes.contacts', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>


 <!-- to top button  -->
  <button class="toTop">
    <i class="fa-light toTop-icon fa-arrow-up"></i>
  </button>
  <!-- end of to top button -->





  <?php $__env->stopSection(); ?>

<?php echo $__env->make('site.layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH D:\Workstation\Taswk\kufa\resources\views/site/index.blade.php ENDPATH**/ ?>