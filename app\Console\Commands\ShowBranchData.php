<?php

namespace Tasawk\Console\Commands;

use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Tasawk\Models\Branch;
use Tasawk\Models\Zone;
use Tasawk\Models\Manager;
use Tasawk\Models\Operator;

class ShowBranchData extends Command
{
    protected $signature = 'show:branch-data';
    protected $description = 'Display branch data and relationships';

    public function handle()
    {
        $this->info('=== BRANCH DATA SUMMARY ===');
        $this->newLine();

        // Show counts
        $this->info('COUNTS:');
        $this->line('Zones: ' . Zone::count());
        $this->line('Branches: ' . Branch::count());
        $this->line('Managers: ' . Manager::count());
        $this->line('Operators: ' . Operator::count());
        $this->line('Branch-Manager relations: ' . DB::table('branch_manager')->count());
        $this->line('Branch-Account relations: ' . DB::table('branch_account')->count());
        $this->newLine();

        // Show zones
        $this->info('ZONES:');
        foreach (Zone::all() as $zone) {
            $this->line("- {$zone->name} (ID: {$zone->id}) - Status: " . ($zone->status ? 'Active' : 'Inactive'));
        }
        $this->newLine();

        // Show branches with relationships
        $this->info('BRANCHES:');
        foreach (Branch::all() as $branch) {
            $this->line("Branch: {$branch->name} (ID: {$branch->id})");
            $this->line("  Phone: {$branch->phone}");
            $this->line("  Email: {$branch->email}");
            $this->line("  Address: {$branch->address_name}");
            
            // Get managers
            $managers = DB::table('branch_manager')
                ->join('users', 'branch_manager.manager_id', '=', 'users.id')
                ->where('branch_manager.branch_id', $branch->id)
                ->pluck('users.name');
            $this->line("  Managers: " . ($managers->isEmpty() ? 'None' : $managers->join(', ')));
            
            // Get operators
            $operators = DB::table('branch_account')
                ->join('users', 'branch_account.account_id', '=', 'users.id')
                ->where('branch_account.branch_id', $branch->id)
                ->pluck('users.name');
            $this->line("  Operators: " . ($operators->isEmpty() ? 'None' : $operators->join(', ')));
            
            $this->line("  Maintenance Mode: " . ($branch->maintenance_mode ? 'Yes' : 'No'));
            $this->line("  Heavy Load Mode: " . ($branch->heavy_load_mode ? 'Yes' : 'No'));
            $this->newLine();
        }

        // Show managers
        $this->info('MANAGERS:');
        foreach (Manager::all() as $manager) {
            $branches = DB::table('branch_manager')
                ->join('branches', 'branch_manager.branch_id', '=', 'branches.id')
                ->where('branch_manager.manager_id', $manager->id)
                ->pluck('branches.name');
            $this->line("- {$manager->name} ({$manager->email}) - Branches: " . ($branches->isEmpty() ? 'None' : $branches->join(', ')));
        }
        $this->newLine();

        // Show operators
        $this->info('OPERATORS:');
        foreach (Operator::all() as $operator) {
            $branches = DB::table('branch_account')
                ->join('branches', 'branch_account.branch_id', '=', 'branches.id')
                ->where('branch_account.account_id', $operator->id)
                ->pluck('branches.name');
            $this->line("- {$operator->name} ({$operator->email}) - Branches: " . ($branches->isEmpty() ? 'None' : $branches->join(', ')));
        }

        return 0;
    }
}
