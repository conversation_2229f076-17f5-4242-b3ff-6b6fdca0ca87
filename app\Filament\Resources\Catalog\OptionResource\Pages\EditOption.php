<?php

namespace Tasawk\Filament\Resources\Catalog\OptionResource\Pages;
use Tasawk\Filament\Resources\Catalog\OptionResource;
use Filament\Actions;
use Filament\Forms\Form;
use Filament\Resources\Pages\EditRecord\Concerns\Translatable;
use Filament\Resources\Pages\EditRecord;

class EditOption extends EditRecord {
    use Translatable, OptionResource\Schema\OptionSchema;

    protected static string $resource = OptionResource::class;

    protected function getHeaderActions(): array {

        return [
            Actions\LocaleSwitcher::make(),
        ];
    }
    protected function getRedirectUrl(): string {
        return $this->getResource()::getUrl("index");
    }
    public function form(Form $form): Form {
        return $form->schema($this->schema());

    }

}
