<?php

namespace Tasawk\Notifications\Branch;

use Illuminate\Bus\Queueable;
use Illuminate\Notifications\Notification;
use Tasawk\Lib\Firebase;
use Tasawk\Lib\NotificationMessageParser;
use Tasawk\Models\Branch;
use Tasawk\Models\DeviceToken;
use Tasawk\Models\Order;

class BranchMaintenanceModeChangedNotification extends Notification {
    use Queueable;

    /**
     * Create a new notification instance.
     */
    public function __construct(public Branch $branch) {
    }

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array {
        return ['database'];
    }


    public function toFirebase($notifiable) {
        $tokens = DeviceToken::where('user_id', $notifiable->id)->pluck('token')->unique()->toArray();
         Firebase::make()
            ->setTokens($tokens)
            ->setTitle($this->getTitle($notifiable)[$notifiable->preferredLocale()]??'')
            ->setBody($this->getBody($notifiable)[$notifiable->preferredLocale()]??'')
            ->setMoreData([
                'entity_id' => $this->branch->id,
                'entity_type' => 'branch',
            ])
            ->do();

    }

    public function toArray($notifiable): array {
        $this->toFirebase($notifiable);


        return [
            'title' => json_encode($this->getTitle($notifiable)),
            'body' => json_encode($this->getBody($notifiable)),
            'format' => 'filament',
            'viewData' => [
                'entity_id' => "{$this->branch?->id}",
                'entity_type' => 'branch',
            ],
            'duration' => 'persistent'
        ];

    }

    public function getTitle($notifiable) {
        return NotificationMessageParser::init($notifiable)
            ->adminMessage('panel.notifications.branch_maintenance_mode_changed', ['branch_name' => $this->branch->name])
            ->parse();
    }

    public function getBody($notifiable) {
        return NotificationMessageParser::init($notifiable)
            ->adminMessage('panel.notifications.branch_maintenance_mode_changed_body', ["branch_name" => $this->branch->name, 'status' => $this->branch->maintenance_mode ? __("panel.messages.activated") : __("panel.messages.deactivated")])
            ->parse();
    }
}
