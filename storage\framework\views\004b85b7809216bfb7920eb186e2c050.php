<header <?php if(request()->route()->getName()!='home'): ?> class='blur-fixed-header' <?php endif; ?>>
    <div class="container">
        <div class="header-content">
            <div class="logo">
                <a href="<?php echo e(route('home')); ?>"> <img src="<?php echo e(url('/storage/' . $landing_page->logo)); ?>"
                                                  alt="logo image"></a>
            </div>
            <div class="navigation">
                <ul class=" big-menu list-unstyled ">
                    <?php if($landing_page->about_description['active'] == 1): ?>

                        <li><a href="<?php echo e(route('home')); ?>#about-sec"> <?php echo e(trans('site.about_us')); ?></a></li>
                    <?php endif; ?>
                    <?php if($landing_page->our_features_description['active'] == 1): ?>
                        <li><a href="<?php echo e(route('home')); ?>#features-sec"> <?php echo e(trans('site.app_feature')); ?> </a></li>
                    <?php endif; ?>
                    <?php if($landing_page->app_screen['active'] == 1): ?>
                        <li><a href="<?php echo e(route('home')); ?>#appScreens-sec"> <?php echo e(trans('site.app_images')); ?> </a></li>
                    <?php endif; ?>
                    <?php if( $landing_page->clients_reviews['active'] == 1): ?>
                        <li><a href="<?php echo e(route('home')); ?>#clientReview-sec"> <?php echo e(trans('site.client_reviews')); ?></a></li>
                    <?php endif; ?>
                    <?php if(($faqs)->count() > 0 && $landing_page->faq['active']): ?>

                        <li><a href="<?php echo e(route('home')); ?>#faq-sec"> <?php echo e(trans('site.faq')); ?> </a></li>
                    <?php endif; ?>
                    <?php if($landing_page->contact_us['active'] ==1): ?>

                        <li><a href="#contact-sec"> <?php echo e(trans('site.contact_us')); ?> </a></li>
                    <?php endif; ?>
                    <li>

                    <?php $__currentLoopData = LaravelLocalization::getSupportedLocales(); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $localeCode => $properties): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <li>
                            <a class="<?php echo \Illuminate\Support\Arr::toCssClasses([
                                    'lang-ancor',
                                    'd-none' => $localeCode == LaravelLocalization::getCurrentLocale(),
                                ]); ?>" rel="alternate" hreflang="<?php echo e($localeCode); ?>"
                               href="<?php echo e(LaravelLocalization::getLocalizedURL($localeCode, null, [], true)); ?>">
                                <?php echo e($properties['native']); ?>

                            </a>
                        </li>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

                        </li>
                </ul>
            </div>
            <div class="links-bars-holder">
                <div class="header-links">
                    <a href="<?php echo e($google_play_link); ?>">
                        <i class="fa-brands google fa-google-play"></i>
                    </a>
                    <a href="<?php echo e($apple_store_link); ?>">
                        <i class="fa-brands apple fa-apple"></i>
                    </a>
                </div>
                <button class="openBtn"><i class="fa-regular fa-bars"></i></button>
            </div>

        </div>
</header>
<?php /**PATH D:\Workstation\Taswk\kufa\resources\views/site/includes/header.blade.php ENDPATH**/ ?>