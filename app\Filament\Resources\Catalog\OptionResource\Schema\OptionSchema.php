<?php

namespace Tasawk\Filament\Resources\Catalog\OptionResource\Schema;

use Filament\Forms\Components\SpatieMediaLibraryFileUpload;
use Tasawk\Enum\Catalog\OptionTypes;
use Tasawk\Lib\Options;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\Hidden;
use Filament\Forms\Components\Repeater;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Toggle;

trait OptionSchema {
    public function schema(): array {
        return [
            Grid::make()->schema([
                Section::make('General')->schema([

                    TextInput::make('name')
                        ->required()
                        ->translateLabel(),

                    Select::make("type")
                        ->options(OptionTypes::class)
                        ->required()
                        ->live(),


                    Toggle::make('status')
                        ->default(1)
                        ->onColor('success')
                        ->offColor('danger')
                ]),


                Section::make('Values')->schema([
                    Repeater::make('values')
                        ->schema($this->valuesSchema())
                        ->label('')
                        ->relationship("values")
                        ->cloneable()
                        ->reorderable()
                        ->orderColumn()
                        ->addActionLabel(__('forms.actions.add'))
                ])->hidden(fn($get) => !$get('type'))

            ])->columns(1)
        ];
    }

    public function valuesSchema(): array {
        $schema = [];
        foreach ((new self())->getTranslatableLocales() as $locale) {
            $schema[] = TextInput::make('value.' . $locale)
                ->label(__('forms.fields.value_name'))
                ->required()
                ->live()
                ->visible(fn($get) => $locale == $this->getActiveActionsLocale())
                ->afterStateHydrated(fn(TextInput $component) => $component->state($component->getModelInstance()->getTranslation('value', $locale)));

            $schema[] = Hidden::make('value.' . $locale)->default(fn($get) => $get('value.' . $locale));
        }
        $schema[] = SpatieMediaLibraryFileUpload::make('image')
            ->image();
        return $schema;
    }

}
