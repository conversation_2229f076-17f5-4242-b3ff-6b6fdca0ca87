<?php

namespace Tasawk\Filament\Resources\Catalog\OptionResource\Pages;

use Tasawk\Filament\Resources\Catalog\OptionResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListOptions extends ListRecords {

    protected static string $resource = OptionResource::class;

    protected function getHeaderActions(): array {
        return [
//            Actions\LocaleSwitcher::make(),

            Actions\CreateAction::make(),
        ];
    }
}
