<?php

namespace Tasawk\Filament\Resources\Catalog\BranchResource\Pages;

use DB;
use MatanYadaev\EloquentSpatial\Objects\Geometry;
use Tasawk\Filament\Resources\Catalog\BranchResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

class EditBranch extends EditRecord {
    use EditRecord\Concerns\Translatable;

    protected static string $resource = BranchResource::class;

    protected function getHeaderActions(): array {
        return [
            Actions\LocaleSwitcher::make(),
        ];
    }
}
