<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void {
        Schema::create('branches', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('phone')->unique();
            $table->string('email')->unique();
            $table->json('receipt_methods');
            $table->text('address_name');
            $table->point('location');
            $table->geometry('boundaries');
            $table->foreignId("manager_id");
            $table->foreignId("zone_id");
            $table->boolean('maintenance_mode');
            $table->boolean('heavy_load_mode');
            $table->json('working_days');
            $table->timestamps();
        });

        Schema::create('inventories', function (Blueprint $table) {
            $table->id();
            $table->foreignId("branch_id");
            $table->foreignId("product_id");
            $table->time('from')->nullable();
            $table->time('to')->nullable();
            $table->boolean('status')->default(1);
            $table->timestamps();
        });
        Schema::create('inventories_options', function (Blueprint $table) {
            $table->id();
            $table->foreignId("inventory_id");
            $table->foreignId("option_id");
            $table->boolean("status")->default(1);
            $table->boolean("required");
            $table->timestamps();

        });
        Schema::create('inventories_options_values', function (Blueprint $table) {
            $table->id();
            $table->foreignId("inventory_option_id");
            $table->foreignId("value_id");
            $table->boolean("status")->default(1);
            $table->float('price',8,3);
            $table->timestamps();
        });

    }

    /**
     * Reverse the migrations.
     */
    public function down(): void {
        Schema::dropIfExists('inventories_options_values');
        Schema::dropIfExists('inventories_options');
        Schema::dropIfExists('inventories');
        Schema::dropIfExists('branches');
    }
};
