<?php

namespace Tasawk\Http\Resources\Api\Customer\Cart;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Tasawk\Http\Resources\Api\Customer\Products\LightProductResource;
use Tasawk\Models\Catalog\Product;


class CartResource extends JsonResource {

    /**
     * Transform the resource into an array.
     *
     * @param Request $request
     * @return array
     */
    public function toArray($request) {
        $products = $this->getContent()->values()->pluck('associatedModel.id')->toArray();
        $recommendations = \DB::table('product_recommendation')->whereIn('product_id',$products)->whereNotIn("recommended_product_id",$products)->pluck('recommended_product_id');;

        return [
            "products" => CartProductResource::collection($this->getContent()->values()),
            'recommendations' => LightProductResource::collection(Product::findMany($recommendations)),
            'totals' => $this->formattedTotals()
        ];
    }

}
