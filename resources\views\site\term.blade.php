@extends('site.layouts.app')
@section('header-page', 'pages-body')
@section('title', trans('site.terms'))
@section('logo')
    <div class="logo">
        <a href="{{ route('home') }}"> <img src="{{  $landing_page->logo }}" alt="logo image"></a>
    </div>
@endsection
@section('content')
    <section class="breadcrumb-sec">
        <div class="container">
            <ol class="breadcrumb">
                <li class="item-home">
                    <a class="bread-link " href="{{ route('home') }}">{{ trans('site.home') }} </a>
                </li>
                <li class="active">
                    <span class="bread-current"> {{ trans('site.terms') }} </span>
                </li>
            </ol>
        </div>
    </section>
    <section class="page-terms-sec page-general-sec">
        <div class="container">
            <h3 class="page-sec-head wow  animate__fadeInDown animate__animated"> {{ trans('site.terms') }} </h3>
            @if (!is_null($term))
                {!! $term->getTranslation('description', app()->getLocale()) !!}
            @endif
        </div>
    </section>

{{--    @include('site.includes.contacts')--}}
@endsection
