
@extends('site.layouts.app')
@section('logo')
    <div class="logo">
        <a href="{{ route('home') }}"> <img src="{{  $landing_page->logo }}" alt="logo image"></a>
    </div>
@endsection
@section('content')
<section class="breadcrumb-sec">
        <div class="container">
            <ol class="breadcrumb">
                <li class="item-home">
                    <a class="bread-link " href="{{ route('home') }}">{{ trans('site.home') }} </a>
                </li>
                <li class="active">
                    <span class="bread-current"> {{ trans('site.faqs') }} </span>
                </li>
            </ol>
        </div>
    </section>
  <section class="faq" id="faq-sec">
    <div class="container">
      <div class="titles-parent">
        <h4 class="sub-title">{{trans('site.faq')}}</h4>
        <h2 class="general-title" >
         {{trans('site.faq_des')}}

        </h2>
      </div>
      <div class="questions-parent">


      @if (!is_null($faqs))
         @foreach($faqs as $faq)

        <div class="main-question ">
          <div class="ques-details">
            <div class="ques-num">
            {{ $loop->iteration }}
            </div>
            <h3 class="ques-text"> {{ $faq->question }}</h3>
            <i class="fa-regular icon fa-plus"></i>
          </div>
          <div class="ques-answer">
          {{ $faq->answer }}
           </div>
        </div>

        @endforeach
        @endif

      </div>
    </div>
  </section>
  <!-- End faq -->


{{--@include('site.includes.contacts')--}}
@endsection
