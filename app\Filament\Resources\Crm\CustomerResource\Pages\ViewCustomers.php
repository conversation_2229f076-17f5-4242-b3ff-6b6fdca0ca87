<?php

namespace Tasawk\Filament\Resources\Crm\CustomerResource\Pages;

use Filament\Actions\Action;
use Filament\Forms\Components\Select;
use Filament\Resources\Pages\ViewRecord;
use Str;
use Tasawk\Api\Facade\Api;
use Tasawk\Enum\OrderPaymentStatus;
use Tasawk\Enum\OrderStatus;
use Tasawk\Filament\Resources\Crm\CustomerResource;
use Tasawk\Filament\Resources\OrderResource;
use Tasawk\Models\CancellationReason;
use Tasawk\Models\Order;
use Tasawk\Notifications\Order\OrderCanceledNotification;

class ViewCustomers extends ViewRecord {
    protected static string $resource = CustomerResource::class;

}
