<?php

namespace Tasawk\Actions\Customer;

use Tasawk\Notifications\Lib\NotificationsSender;
use Tasawk\Orders\Models\Order;
use Tasawk\Orders\Notifications\NewOrderNotification;
use Tasawk\Utilities\Lib\Action;

class OrderCreatedAction extends Action {

    public function __construct(Order $order) {
        $order->decreaseItemsQtyIfStockEnabled();
        new NotificationsSender(
            'orders',
            "new_order",
            new NewOrderNotification($order)
        );
    }
}
