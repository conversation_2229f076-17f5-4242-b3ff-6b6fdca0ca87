<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;
use Tasawk\Models\Catalog\Product;
use Tasawk\Models\Catalog\Category;
use Tasawk\Models\Catalog\Branch\Inventory;
use Tasawk\Models\AddressBook;
use Tasawk\Models\Customer;
use Tasawk\Models\Branch;
use Tasawk\Models\Zone;

class SampleDataSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create a sample customer
        $customer = Customer::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Ahmed Al-Kuwaiti',
                'phone' => '+96512345678',
                'password' => Hash::make('password123'),
                'active' => true,
                'phone_verified_at' => now(),
            ]
        );

        // Create categories
        $categories = [
            ['name' => ['en' => 'Main Dishes', 'ar' => 'الأطباق الرئيسية'], 'status' => true],
            ['name' => ['en' => 'Appetizers', 'ar' => 'المقبلات'], 'status' => true],
            ['name' => ['en' => 'Beverages', 'ar' => 'المشروبات'], 'status' => true],
            ['name' => ['en' => 'Desserts', 'ar' => 'الحلويات'], 'status' => true],
        ];

        $categoryIds = [];
        foreach ($categories as $categoryData) {
            $category = Category::firstOrCreate(
                ['name->en' => $categoryData['name']['en']],
                $categoryData
            );
            $categoryIds[] = $category->id;
        }

        // Create sample products
        $products = [
            [
                'title' => ['en' => 'Grilled Chicken', 'ar' => 'دجاج مشوي'],
                'description' => ['en' => 'Delicious grilled chicken with herbs', 'ar' => 'دجاج مشوي لذيذ بالأعشاب'],
                'price' => 12.500,
                'sale_price' => 10.000,
                'status' => true,
                'category_id' => $categoryIds[0] ?? 1,
            ],
            [
                'title' => ['en' => 'Hummus Plate', 'ar' => 'طبق حمص'],
                'description' => ['en' => 'Traditional hummus with olive oil', 'ar' => 'حمص تقليدي بزيت الزيتون'],
                'price' => 4.500,
                'sale_price' => 4.500,
                'status' => true,
                'category_id' => $categoryIds[1] ?? 1,
            ],
            [
                'title' => ['en' => 'Fresh Orange Juice', 'ar' => 'عصير برتقال طازج'],
                'description' => ['en' => 'Freshly squeezed orange juice', 'ar' => 'عصير برتقال طازج معصور'],
                'price' => 3.000,
                'sale_price' => 3.000,
                'status' => true,
                'category_id' => $categoryIds[2] ?? 1,
            ],
            [
                'title' => ['en' => 'Baklava', 'ar' => 'بقلاوة'],
                'description' => ['en' => 'Sweet pastry with nuts and honey', 'ar' => 'معجنات حلوة بالمكسرات والعسل'],
                'price' => 6.000,
                'sale_price' => 5.500,
                'status' => true,
                'category_id' => $categoryIds[3] ?? 1,
            ],
            [
                'title' => ['en' => 'Lamb Kabsa', 'ar' => 'كبسة لحم'],
                'description' => ['en' => 'Traditional rice dish with lamb', 'ar' => 'طبق أرز تقليدي باللحم'],
                'price' => 18.000,
                'sale_price' => 18.000,
                'status' => true,
                'category_id' => $categoryIds[0] ?? 1,
            ],
        ];

        $productIds = [];
        foreach ($products as $productData) {
            $categoryId = $productData['category_id'];
            unset($productData['category_id']);

            $product = Product::firstOrCreate(
                ['title->en' => $productData['title']['en']],
                $productData
            );

            // Attach to category
            $product->categories()->syncWithoutDetaching([$categoryId]);

            $productIds[] = $product->id;
        }

        // Create inventory for products in all branches
        $branches = Branch::all();
        foreach ($branches as $branch) {
            foreach ($productIds as $productId) {
                Inventory::firstOrCreate(
                    [
                        'product_id' => $productId,
                        'branch_id' => $branch->id,
                    ],
                    [
                        'status' => true,
                    ]
                );
            }
        }

        // Create sample addresses for the customer
        $zones = Zone::all();
        if ($zones->isNotEmpty()) {
            $addresses = [
                [
                    'name' => 'Home',
                    'user_id' => $customer->id,
                    'zone_id' => $zones->first()->id,
                    'state' => 'Kuwait',
                    'street' => 'Salem Al-Mubarak Street',
                    'building_number' => '123',
                    'floor' => '2',
                    'phone' => '+96512345678',
                    'map_location' => ['lat' => 29.3759, 'lng' => 47.9774],
                    'notes' => 'Main entrance, apartment 2A',
                    'primary' => true,
                ],
                [
                    'name' => 'Office',
                    'user_id' => $customer->id,
                    'zone_id' => $zones->count() > 1 ? $zones[1]->id : $zones->first()->id,
                    'state' => 'Kuwait',
                    'street' => 'Ahmad Al-Jaber Street',
                    'building_number' => '456',
                    'floor' => '5',
                    'phone' => '+96512345679',
                    'map_location' => ['lat' => 29.3375, 'lng' => 47.6581],
                    'notes' => 'Office building, floor 5',
                    'primary' => false,
                ],
            ];

            foreach ($addresses as $addressData) {
                AddressBook::firstOrCreate(
                    [
                        'name' => $addressData['name'],
                        'user_id' => $addressData['user_id'],
                    ],
                    $addressData
                );
            }
        }

        $this->command->info('Sample data created successfully!');
        $this->command->info('Customer: ' . $customer->email . ' (ID: ' . $customer->id . ')');
        $this->command->info('Products created: ' . count($productIds));
        $this->command->info('Categories created: ' . count($categoryIds));
        $this->command->info('Addresses created: ' . AddressBook::where('user_id', $customer->id)->count());
    }
}
