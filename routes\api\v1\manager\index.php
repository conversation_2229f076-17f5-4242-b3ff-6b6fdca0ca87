<?php

use Tasawk\Api\V1\Manager\AuthServices;
use Tasawk\Api\V1\Manager\BranchServices;

Route::prefix('v1/manager')->group(function () {
    require_once __DIR__ . '/auth.php';
    require_once __DIR__ . '/profile.php';

    Route::middleware('auth:sanctum')->group(function (){
       Route::post("branch/maintenance-mode/toggle",[BranchServices::class,'toggleMaintenanceMode']);
       Route::post("branch/heavy-load-mode/toggle",[BranchServices::class,'toggleHeavyLoadMode']);
       Route::get("branch/statistics/today",[BranchServices::class,'todayStatistics']);
       Route::get("branch/orders",[BranchServices::class,'orders']);
       Route::get("branch/orders/{order}",[BranchServices::class,'order']);
       Route::get("branch/orders/{order}/status",[BranchServices::class,'getOrderStatuses']);
       Route::put("branch/orders/{order}/status",[BranchServices::class,'changeOrderStatus']);

        Route::get("branch/products",[BranchServices::class,'products']);
        Route::get("branch/products/{product}/options",[BranchServices::class,'productOptions']);

        Route::post("branch/products/{product}/status/toggle",[BranchServices::class,'toggleProductStatus']);
       Route::post("branch/products/{product}/options/{option}/status/toggle",[BranchServices::class,'toggleProductOptionStatus']);
       Route::post("branch/products/{product}/options/{option}/values/{value}/status/toggle",[BranchServices::class,'toggleProductOptionValueStatus']);
       Route::get("branch/working-days",[BranchServices::class,'workingDays']);
       Route::get("branch/working-days-ii",[BranchServices::class,'workingDaysV2']);

    });
});
