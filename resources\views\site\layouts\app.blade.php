<!DOCTYPE html>
<html lang="{{app()->getLocale()}}" dir={{ app()->getLocale() == 'en' ? 'ltr' : 'rtl' }}>

<head>
    <meta charset="UTF-8"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge"/>
    <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
    <link rel="icon" href="{{ asset("storage/".$landing_page->logo) }}" type="image/webp"/>
    <title>{{(new \Tasawk\Settings\GeneralSettings())->app_name}}</title>
    @include('site.includes.style')

    @livewireStyles

</head>

<body data-bs-spy="scroll" data-bs-target="#navbar-example">
  <!-- preloader -->
  <div class="preloader">
    <div class="progress">
      <div class="progress-bar"></div>
    </div>
  </div>

  @include('site.includes.header')

  <!-- end preloader -->
<!-- end preloader -->
<!-- header -->
<!-- end of header -->

@yield('content')

@include('site.includes.footer')
<!-- to top button  -->
<button class="toTop">
    <i class="fa-light toTop-icon fa-arrow-up"></i>
</button>
  <script>
      const home_page_route= @js(request()->route()->getName() =='home');
  </script>
@include('site.includes.script')
@livewireScripts

</body>

</html>
