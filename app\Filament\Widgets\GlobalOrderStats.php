<?php

namespace Tasawk\Filament\Widgets;

use <PERSON><PERSON><PERSON><PERSON><PERSON>h\FilamentShield\Traits\HasWidgetShield;
use Cknow\Money\Money;
use DB;
use Filament\Widgets\Concerns\InteractsWithPageTable;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;
use Tasawk\Enum\OrderStatus;
use Tasawk\Filament\Resources\OrderResource\Pages\ListOrders;

class GlobalOrderStats extends BaseWidget {
    use HasWidgetShield;

    protected static ?int $sort = 1;

    protected function getStats(): array {
        $stats = DB::table('orders')->select(
            DB::raw("SUM(
		CASE WHEN status = 'pending_for_accept_order'
		THEN
			total
		ELSE
			0
		END) AS 'new',
		SUM(
		CASE WHEN status in( 'pending_for_customer_pay','received_and_preparing','prepared','on_way' )
		THEN
			total
		ELSE
			0
		END) AS 'in_processing',
		SUM(
		CASE WHEN status = 'delivered'
		THEN
			total
		ELSE
			0
		END) AS 'delivered',

			SUM(
		CASE WHEN status = 'canceled'
		THEN
			total
		ELSE
			0
		END) AS 'canceled'
"))->first();
//        Money::setLocale(session()->get('locale'));
        return [

            Stat::make(__('panel.stats.new_orders_total'), Money::parse($stats->new)->format()),
            Stat::make(__('panel.stats.in_processing_orders_total'), Money::parse($stats->in_processing)->format()),
            Stat::make(__('panel.stats.completed_orders_total'), Money::parse($stats->delivered)->format()),
            Stat::make(__('panel.stats.canceled_orders_total'), Money::parse($stats->canceled)->format()),
        ];
    }


}
