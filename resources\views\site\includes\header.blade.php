<header @if(request()->route()->getName()!='home') class='blur-fixed-header' @endif>
    <div class="container">
        <div class="header-content">
            <div class="logo">
                <a href="{{route('home')}}"> <img src="{{ url('/storage/' . $landing_page->logo)}}"
                                                  alt="logo image"></a>
            </div>
            <div class="navigation">
                <ul class=" big-menu list-unstyled ">
                    @if($landing_page->about_description['active'] == 1)

                        <li><a href="{{ route('home') }}#about-sec"> {{ trans('site.about_us') }}</a></li>
                    @endif
                    @if($landing_page->our_features_description['active'] == 1)
                        <li><a href="{{ route('home') }}#features-sec"> {{ trans('site.app_feature') }} </a></li>
                    @endif
                    @if($landing_page->app_screen['active'] == 1)
                        <li><a href="{{ route('home') }}#appScreens-sec"> {{ trans('site.app_images') }} </a></li>
                    @endif
                    @if( $landing_page->clients_reviews['active'] == 1)
                        <li><a href="{{ route('home') }}#clientReview-sec"> {{ trans('site.client_reviews') }}</a></li>
                    @endif
                    @if(($faqs)->count() > 0 && $landing_page->faq['active'])

                        <li><a href="{{ route('home') }}#faq-sec"> {{ trans('site.faq') }} </a></li>
                    @endif
                    @if($landing_page->contact_us['active'] ==1)

                        <li><a href="#contact-sec"> {{ trans('site.contact_us') }} </a></li>
                    @endif
                    <li>

                    @foreach(LaravelLocalization::getSupportedLocales() as $localeCode => $properties)
                        <li>
                            <a @class([
                                    'lang-ancor',
                                    'd-none' => $localeCode == LaravelLocalization::getCurrentLocale(),
                                ]) rel="alternate" hreflang="{{ $localeCode }}"
                               href="{{ LaravelLocalization::getLocalizedURL($localeCode, null, [], true) }}">
                                {{ $properties['native'] }}
                            </a>
                        </li>
                        @endforeach

                        </li>
                </ul>
            </div>
            <div class="links-bars-holder">
                <div class="header-links">
                    <a href="{{ $google_play_link }}">
                        <i class="fa-brands google fa-google-play"></i>
                    </a>
                    <a href="{{ $apple_store_link }}">
                        <i class="fa-brands apple fa-apple"></i>
                    </a>
                </div>
                <button class="openBtn"><i class="fa-regular fa-bars"></i></button>
            </div>

        </div>
</header>
