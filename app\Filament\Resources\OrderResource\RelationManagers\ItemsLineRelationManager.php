<?php

namespace Tasawk\Filament\Resources\OrderResource\RelationManagers;

use Blade;
use Cknow\Money\Money;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Tasawk\Models\Order\ItemsLine;
use Tasawk\Traits\Filament\HasTranslationLabel;

class ItemsLineRelationManager extends RelationManager {
    protected static string $relationship = 'itemsLine';

    public function form(Form $form): Form {
        return $form
            ->schema([

            ]);
    }

    public function table(Table $table): Table {
        return $table
            ->heading(__('sections.products'))
            ->recordTitleAttribute('name')
            ->columns([
                Tables\Columns\TextColumn::make('model.title.'.app()->getLocale())
                    ->label(__('sections.products'))
                    ->description(function ($record) {
                        $text = [];
                        foreach ($record['attributes']['options']??[] as $index => $option) {
                            $text[] = "{$option['name'][app()->getLocale()]} : {$option['value'][app()->getLocale()]} ({$option['price']}) ";
                        }
                        return \View::make('filament.ul-list', ['list' => $text]);


                    }),

                Tables\Columns\TextColumn::make('quantity'),
                Tables\Columns\TextColumn::make('price')
                    ->state(function (ItemsLine $record) {
                        return Money::parse($record->price)->format();
                    }),

                Tables\Columns\TextColumn::make('total')
                    ->state(function (ItemsLine $record) {
                        return Money::parse(($record->quantity * $record->price)+($record->quantity * collect($record->conditions)->sum('value')))->format();
                    }),

            ])
            ->filters([
                //
            ])
            ->headerActions([
                Tables\Actions\CreateAction::make(),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }


}
