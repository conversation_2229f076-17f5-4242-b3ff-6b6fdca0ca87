<form class="myform" wire:submit.prevent="submitForm">
    <!--[if BLOCK]><![endif]--><?php if($successMessage): ?>
        <div class="alert alert-success" role="alert">
            <?php echo e($successMessage); ?>

        </div>
    <?php endif; ?><!--[if ENDBLOCK]><![endif]-->
    <div class="inputs">
        <div>
            <input type="text" wire:model="name" placeholder="<?php echo e(trans('site.full_name')); ?>*"/>
            <!--[if BLOCK]><![endif]--><?php $__errorArgs = ['name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
            <small class="text-danger">
                <?php echo e($message); ?>

            </small><?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?><!--[if ENDBLOCK]><![endif]-->
        </div>

        <div>
            <div>
                <input type="text" wire:model="phone" placeholder="<?php echo e(trans('site.phone')); ?>*"/>
            </div>
            <!--[if BLOCK]><![endif]--><?php $__errorArgs = ['phone'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
            <small class="text-danger">
                <?php echo e($message); ?>

            </small>
            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?><!--[if ENDBLOCK]><![endif]-->
        </div>
        <div>
            <div wire:ignore>
                <select class="myselect" wire:model="city_id">
                    <option selected value="0"> <?php echo e(trans('site.choose_city')); ?>*</option>
                    <!--[if BLOCK]><![endif]--><?php $__currentLoopData = $cities; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $city): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <option value="<?php echo e($city->id); ?>"><?php echo e($city->name); ?></option>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?><!--[if ENDBLOCK]><![endif]-->
                </select>

            </div>
            <!--[if BLOCK]><![endif]--><?php $__errorArgs = ['city_id'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
            <small class="text-danger">
                <?php echo e($message); ?>

            </small>
            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?><!--[if ENDBLOCK]><![endif]-->
        </div>
        <div>
            <div>

                <input type="text" wire:model="email" placeholder="<?php echo e(trans('site.email')); ?>*"/>
            </div>
            <!--[if BLOCK]><![endif]--><?php $__errorArgs = ['email'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
            <small class="text-danger">
                <?php echo e($message); ?>

            </small>
            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?><!--[if ENDBLOCK]><![endif]-->
        </div>

        <textarea wire:model="message" placeholder="<?php echo e(trans('site.contact_massage')); ?>*"></textarea>
        <!--[if BLOCK]><![endif]--><?php $__errorArgs = ['message'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
        <small class="text-danger">
            <?php echo e($message); ?>

        </small>
        <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?><!--[if ENDBLOCK]><![endif]-->

    </div>

    <div class="submit-holder">
        <button wire:loading.attr="disabled" wire:target="submitForm" class="apply-btn">
            <span wire:loading.remove wire:target="submitForm"> <?php echo e(trans('site.send')); ?></span>
            <span wire:loading wire:target="submitForm"> <?php echo e(trans('site.send')); ?>....</span>
        </button>
    </div>
</form>
<script>

    document.addEventListener('DOMContentLoaded', function () {
        $('.myselect').on('change', function (e) {
            var data = $('.myselect').select2("val");
            window.Livewire.find('<?php echo e($_instance->getId()); ?>').
            set('city_id', data);
        });
    });
</script>
<?php /**PATH D:\Workstation\Taswk\kufa\resources\views/livewire/contact-us.blade.php ENDPATH**/ ?>