<?php

namespace Tasawk\Filament\Resources\Employees\OperatorResource\Pages;

use Tasawk\Filament\Resources\Employees\OperatorResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListOperators extends ListRecords
{
    protected static string $resource = OperatorResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
