<?php


use Tasawk\Api\V1\Manager\ProfileServices;

Route::middleware(["auth:sanctum"])->group(function () {

    Route::get('profile', [ProfileServices::class, 'index']);

    Route::post('profile', [ProfileServices::class, 'update']);

    Route::post('profile/update-password', [ProfileServices::class, 'updatePassword']);

    Route::post('profile/settings', [ProfileServices::class, 'settings']);

});
