<?php

namespace Tasawk\Actions\Customer;

use Exception;
use Lorisle<PERSON>\Actions\Concerns\AsAction;
use Notification;
use Tasawk\Models\Customer;

class RegisterCustomer {
    use AsAction;


    /**
     * @throws Exception
     */
    public function handle($full_name, $phone, $email, $password, $device_token = null) {
        $customer = Customer::create([
            'name' => $full_name,
            'email' => $email,
            'phone' => $phone,
            'password' => $password,
        ]);

        if ($device_token) {
            $customer->deviceTokens()->create(['token' => $device_token]);
        }
//        TODO: uncomment this line to send notification to admins
//        Notification::send(\AdminBase::administrationsUsers(), new NewCustomerRegisteredNotification($customer));
        return $customer;
    }

}

