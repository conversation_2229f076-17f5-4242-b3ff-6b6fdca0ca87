<?php

use <PERSON><PERSON>\LaravelSettings\Migrations\SettingsMigration;

return new class extends SettingsMigration {
    public function up(): void {
        $this->migrator->add('general.app_logo', '');
        $this->migrator->add('general.app_name', '<PERSON>');
        $this->migrator->add('general.app_email', '<EMAIL>');
        $this->migrator->add('general.app_address', 'Riyadh, Saudi Arabia');
        $this->migrator->add('general.app_phone', '5123456789');
        $this->migrator->add('general.taxes', 14);
        $this->migrator->add('general.working_days', [[
            "friday" => [
                "to" => "23:59",
                "from" => "00:00",
                "status" => true,
                "day_name" => "friday"
            ],
            "monday" => [
                "to" => "23:59",
                "from" => "00:00",
                "status" => true,
                "day_name" => "monday"
            ],
            "sunday" => [
                "to" => "23:59",
                "from" => "00:00",
                "status" => true,
                "day_name" => "sunday"
            ],
            "tuesday" => [
                "to" => "23:59",
                "from" => "00:00",
                "status" => true,
                "day_name" => "tuesday"
            ],
            "saturday" => [
                "to" => "23:59",
                "from" => "00:00",
                "status" => true,
                "day_name" => "saturday"
            ],
            "thursday" => [
                "to" => "23:59",
                "from" => "00:00",
                "status" => true,
                "day_name" => "thursday"
            ],
            "wednesday" => [
                "to" => "23:59",
                "from" => "00:00",
                "status" => true,
                "day_name" => "wednesday"
            ]
        ]]);
        $this->migrator->add('general.enable_delivery_mode', true);
        $this->migrator->add('general.min_time_to_deliver_order', 20);
        $this->migrator->add('general.max_time_to_deliver_order', 25);
        $this->migrator->add('general.enable_orders_discount_upon_receipt_from_the_branch', true);
        $this->migrator->add('general.delivery_cost', 20);
        $this->migrator->add('general.diameter', 15);
        $this->migrator->add('general.orders_discount_upon_receipt_from_the_branch', 5);
        $this->migrator->add('general.delivery_cost_for_each_additional_kilometer', 10);
        $this->migrator->add('general.order_limit_per_item', 10);
        $this->migrator->add('general.applications_links', [
            'google_play_link' => 'https://www.google.com',
            'apple_store_link' => 'https://www.apple.com',
        ]);
        $this->migrator->add('general.app_pages', [
            'about_us' => 1,
            'terms_and_conditions' => 2,
            'privacy_policy' => 3,
        ]);
        $this->migrator->add('general.social_links', []);
    }
};
