<?php

use Tasawk\Api\Core;
use Tasawk\Lib\Utils;
use Tasawk\Models\Order;
use Tasawk\Models\Branch;
use Tasawk\Enum\OrderStatus;
use Tasawk\Mail\MyTestEmail;
use Tasawk\Models\Catalog\Product;
use Illuminate\Support\Facades\Log;
use Tasawk\Models\Catalog\Category;
use Illuminate\Support\Facades\Route;
use Tasawk\Http\Controllers\HomeController;
use Illuminate\Support\Facades\Notification;
use MyFatoorah\Library\PaymentMyfatoorahApiV2;

use Tasawk\Notifications\Order\NewOrderCreatedNotification;
use Mcamara\LaravelLocalization\Facades\LaravelLocalization as GlobalLaravelLocalization;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "web" middleware group. Make something great!
|
*/

Route::get('import', function () {

});
Route::redirect('/', 'admin');
Route::view('test', 'welcome');

Route::get('test', function () {
    $product = Product::find(32);
    request()->merge([
        'products' => [
            [
                'id' => $product->id,
                'quantity' => 2,
            ]
        ],
    ]);
    $cart= \Tasawk\Actions\Customer\BuildCartInstanceAction::run();
    return \Tasawk\Http\Resources\Api\Customer\Cart\CartResource::make($cart);

});
//Route::get('categories/arrange', function () {
//    foreach (request()->get("list") as $record) {
//
//        Category::find($record['id'])->update(['parent_id' => $record['parent'] ?? null]);
//    }
//})->name('cp.categories.arrange');
//
//Route::get('test', function () {
//    $recipient = auth()->user();
//
//    \Filament\Notifications\Notification::make()
//        ->title('Saved successfully')
//        ->viewData(['message' => 'Saved successfully'])
//        ->sendToDatabase($recipient);
//    dd('aa');
//    $branch = \Tasawk\Models\Branch::first();
////    \Notification::send(Utils::getAdministrationUsers(), new BranchMaintenanceModeChangedNotification($branch));
//    $record = \Tasawk\Models\Order::first();
//    $statuses = collect(OrderStatus::cases())->map(fn($status) => $status->value);
//    $currentStatusIndex = $statuses->search($record->status->value);
//    $currentValueIndex = $statuses->search('pending_for_customer_pay');
//    dd($currentValueIndex, $currentStatusIndex);
////    dd();
//    dd(collect(OrderStatus::cases())->map(fn($status) => $status->value)->search('received_and_preparing'));
//    $branch = \Tasawk\Models\Branch::first();
//    /**
//     * @var MatanYadaev\EloquentSpatial\Objects\Polygon $poygon
//     * */
////    $poygon = $branch->boundaries;
////    dd($poygon->toFeatureCollectionJson());
////    dd(DatabaseNotification::first());
//    $user = User::first();
//    return dd($user->notifications()->first()->title);
////    return \Tasawk\Models\Manager::first()->branch();
////    return \Tasawk\Models\Branch::first()->products()->first()->productBranchOptions;
//});
Route::put('admin/fcm', function () {
    auth()->user()->deviceTokens()->updateOrCreate(['token' => \request()->get('device_token')]);
    return 200;
})->middleware('auth')->name('admin.fcm-token');


Route::get('orders/{order}/invoice', function (Order $order) {
    return view('filament.pages.print', ['order' => $order]);
})->name('orders.invoice');

Route::get('orders/{order}/invoice/download', function (Order $order) {
//    return pdf()
//        ->view('filament.pages.print', compact('order'))
//        ->name('invoice-2023-04-10.pdf')
//        ->download();

    $pdf = PDF::loadView('filament.pages.print', compact('order'));
    $date = now()->format('Y-m-d');
    return $pdf->download("invoice-$date.pdf");


})->name('orders.invoice.download');


Route::get('webhooks/myfatoorah/callback', function (PaymentMyfatoorahApiV2 $myfatoorahApiV2) {
    $response = $myfatoorahApiV2->getPaymentStatus(request()->get('Id'), 'PaymentId');
    $order = Order::where('payment_data->invoiceId', $response->InvoiceId)->first();
    if ($response->InvoiceStatus == 'Paid') {
        if ($order->payment_status != 'paid') {
            ///send notification
            $order->update([
                'status' => OrderStatus::RECEIVED_AND_PREPARING,
                'payment_data' => array_merge($order->payment_data, [...collect($response)->toArray(), 'method' => $response->focusTransaction->PaymentGateway, 'paid_at' => now()]),
                'payment_status' => 'paid',

            ]);

            Notification::send(Utils::getAdministrationUsers($order->branch_id), new NewOrderCreatedNotification($order));


        }

        return Api::isOk('Payment is done successfully');
    }
    return Api::isError('something went wrong');
})->name('webhooks.myfatoorah.callback');
Route::group([
    'prefix' => GlobalLaravelLocalization::setLocale(),
    'middleware' => ['localeSessionRedirect', 'localizationRedirect', 'localeViewPath']
], function () {
    Route::get('/', [HomeController::class, 'index'])->name('home');
    Route::get('/about-us', [HomeController::class, 'aboutUs'])->name('page.about-us');
    Route::get('/faq', [HomeController::class, 'faqs'])->name('page.faqs');
    Route::get('/term', [HomeController::class, 'term'])->name('page.term');
    Route::get('/privacy-policy', [HomeController::class, 'privacyPolicy'])->name('page.privacy-policy');
});
Route::group(['prefix' => GlobalLaravelLocalization::setLocale()], function () {
    // Your other localized routes...

    Livewire::setUpdateRoute(function ($handle) {
        return Route::post('/livewire/update', $handle);
    });
});
