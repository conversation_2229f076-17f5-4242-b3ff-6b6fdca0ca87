<?php

namespace Tasawk\Http\Resources\Api\Customer\Cart;

use Cknow\Money\Money;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Str;


class CartProductOptionResource extends JsonResource {

    /**
     * Transform the resource into an array.
     *
     * @param Request $request
     * @return array
     */
    public function toArray($request) {
        return [
            'id' => $this['id'],
            'name' => $this['name'][app()->getLocale()]??'',
            'price' => Money::parse($this['price'])->format(),
            'value' => $this['value'][app()->getLocale()]??'',
            'value_id' => $this['value_id'],

        ];
    }

}
