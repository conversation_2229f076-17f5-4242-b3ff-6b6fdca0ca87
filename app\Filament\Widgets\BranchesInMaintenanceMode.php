<?php

namespace Tasawk\Filament\Widgets;

use <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\FilamentShield\Traits\HasWidgetShield;
use Filament\Tables;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;
use Filament\Widgets\TableWidget as BaseWidget;
use Tasawk\Models\Branch;

class BranchesInMaintenanceMode extends BaseWidget {
    use HasWidgetShield;
    protected static ?int $sort = 9;

    public function table(Table $table): Table {
        return $table
            ->heading(__('sections.branches_in_maintenance_mode'))
            ->description(__('sections.branches_in_maintenance_mode_description'))
            ->query(
                Branch::where("maintenance_mode", 1),
            )
            ->columns([
                TextColumn::make('index')->rowIndex()->toggleable(false),
                TextColumn::make('name')->toggleable(false),
            ]);
    }
    public function getTableHeading(): ?string {
        return __('sections.branches_in_maintenance_mode');
    }
}
