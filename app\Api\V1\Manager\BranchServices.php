<?php

namespace Tasawk\Api\V1\Manager;

use Cknow\Money\Money;
use Tasawk\Api\Core;
use Tasawk\Api\Facade\Api;
use Tasawk\Enum\OrderStatus;
use Tasawk\Enum\ReceiptMethods;
use Tasawk\Http\Requests\Api\Manager\ChangeOrderStatusRequest;
use Tasawk\Http\Resources\Api\Manager\Orders\LightOrderResource;
use Tasawk\Http\Resources\Api\Manager\Orders\OrdersResource;
use Tasawk\Http\Resources\Api\Manager\ProductOptionsResource;
use Tasawk\Http\Resources\Api\Manager\ProductResource;
use Tasawk\Http\Resources\Api\Manager\WorkingDayResource;
use Tasawk\Models\CancellationReason;
use Tasawk\Models\Catalog\Branch\Inventory;
use Tasawk\Models\Catalog\Branch\InventoryOption;
use Tasawk\Models\Catalog\Branch\InventoryOptionValue;
use Tasawk\Models\Manager;
use Tasawk\Models\Order;


class BranchServices {
    public function toggleMaintenanceMode(): Core {

        auth()->user()->actAs(Manager::class)->toggleMaintenanceMode();
        return Api::isOk('Done');

    }

    public function toggleHeavyLoadMode(): Core {

        auth()->user()->actAs(Manager::class)->toggleHeavyLoadMode();
        return Api::isOk('Done');

    }

    public function orders() {
        $types = request()->get('type') == 'delivery' ? [ReceiptMethods::DELIVERY->value] : [ReceiptMethods::TAKEAWAY->value, ReceiptMethods::Local->value];
        $orders = auth()->user()->actAs(Manager::class)
            ->mainBranch()
            ->orders()
            ->when(request()->has('type'), fn($builder) => $builder->whereIn('receipt_method', $types))
            ->latest()
            ->paginate();

        return Api::isOk('orders List', LightOrderResource::collection($orders));
    }

    public function order(Order $order) {
        return Api::isOk('orders List', OrdersResource::make($order));
    }

    public function getOrderStatuses(Order $order) {

        return Api::isOk('orders List', $order->getAvailableStatus());
    }

    public function changeOrderStatus(ChangeOrderStatusRequest $request, Order $order) {
        if ($request->isCancelStatus()) {
            $order->cancel($request->string('status')->after("-"));
            return Api::isOk('changed');
        }
        if ($request->string('status') == OrderStatus::DELIVERED->value && $order->payment_data['gateway'] == 'cash') {
            $order->update([
                'payment_data' => array_merge($order->payment_data, ['method' => 'cash', 'paid_at' => now()]),
                'payment_status' => 'paid',
            ]);
        }
        $order->update(['status' => $request->input('status')]);
        return Api::isOk('changed');
    }

    public function products() {
        return Api::isOk('products List', ProductResource::collection(
            auth()->user()->actAs(Manager::class)
                ->mainBranch()
                ->inventories()
                ->filtered()
                ->latest()
                ->paginate()));
    }

    public function productOptions(Inventory $product) {
        return Api::isOk('products List', ProductOptionsResource::collection($product->options()->whereHas('option')->get()));
    }

    public function toggleProductStatus(Inventory $product): Core {
        $product->update(['status' => !$product->status]);
        return Api::isOk("Done");
    }

    public function toggleProductOptionStatus(Inventory $product, InventoryOption $option): Core {
        $option->update(['status' => !$option->status]);
        return Api::isOk("Done");
    }

    public function toggleProductOptionValueStatus(Inventory $product, InventoryOption $option, InventoryOptionValue $value): Core {
        $value->update(['status' => !$value->status]);
        return Api::isOk("Done");
    }

    public function workingDays() {
        $workingDays = collect(auth()->user()->actAs(Manager::class)->mainBranch()->working_days)->values();

        $workingDaysSlots = $workingDays
            ->map(fn($slot) => array_values($slot))
            ->map(fn($slot) => collect($slot)->filter(fn($el) => $el['status'])->values())
            ->values();
        $working_days = collect(WorkingDayResource::collection($workingDaysSlots)->toArray(request()))->mapWithKeys(fn($day, $index) => [$index == 0 ? 'morning' : "evening" => $day]);

        return Api::isOk('working days', $working_days);
    }
    public function workingDaysV2() {
        $workingDays = collect(auth()->user()->actAs(Manager::class)->mainBranch()->working_days)->values();

        $workingDaysSlots = $workingDays
            ->map(fn($slot) => array_values($slot))
            ->map(fn($slot) => collect($slot)->filter(fn($el) => $el['status'])->values())
            ->values();
        $working_days = collect(WorkingDayResource::collection($workingDaysSlots)->toArray(request()));

        return Api::isOk('working days', $working_days);
    }

    public function todayStatistics() {
        $orders = auth()->user()->actAs(Manager::class)->mainBranch()->orders()->today()->get();
        return Api::isOk("statistics", [
            'orders_total' => $orders->reduce(fn($carry, $order) => $order->total->add(Money::parse($carry)), Money::parse(0))->format(),
            'new_orders_count' => $orders->where('status', OrderStatus::PENDING_FOR_ACCEPT_ORDER)->count(),
            'delivery_orders_count' => $orders->where('receipt_method', ReceiptMethods::DELIVERY)->count(),
            'takeaway_orders_count' => $orders->where('receipt_method', ReceiptMethods::TAKEAWAY)->count(),
            'in_site_orders_count' => $orders->where('receipt_method', ReceiptMethods::Local)->count(),
            'in_processing_orders_count' => $orders->whereIn('status', [OrderStatus::RECEIVED_AND_PREPARING, OrderStatus::PREPARED, OrderStatus::ON_WAY])->count(),
            'delivered_orders_count' => $orders->where('status', OrderStatus::DELIVERED)->count(),
            'canceled_orders_count' => $orders->where('status', OrderStatus::CANCELED)->count(),
        ]);
    }
}
