<?php

namespace Tasawk\Filament\Resources\Catalog\ProductResource\Pages;

use Tasawk\Filament\Resources\Catalog\ProductResource;
use Filament\Actions;
use Filament\Resources\Pages\CreateRecord;
use Filament\Resources\Pages\CreateRecord\Concerns\Translatable;

class CreateProduct extends CreateRecord {
    use Translatable;

    protected function getHeaderActions(): array {

        return [
            Actions\LocaleSwitcher::make(),
        ];
    }

    protected static string $resource = ProductResource::class;
}
