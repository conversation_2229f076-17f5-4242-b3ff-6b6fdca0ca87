<?php

namespace Tasawk\Filament\Resources\Employees\ManagerResource\Pages;

use Tasawk\Filament\Resources\Employees\ManagerResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

class EditManager extends EditRecord
{
    protected static string $resource = ManagerResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\DeleteAction::make(),
        ];
    }
}
