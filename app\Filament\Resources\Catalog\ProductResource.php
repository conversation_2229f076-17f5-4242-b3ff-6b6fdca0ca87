<?php

namespace Tasawk\Filament\Resources\Catalog;

use Filament\Notifications\Notification;
use Filament\Tables\Columns\ImageColumn;
use Filament\Tables\Filters\Filter;
use Filament\Tables\Filters\SelectFilter;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Tasawk\Enum\ModelStatus;
use Tasawk\Enum\OrderStatus;
use Tasawk\Filament\Resources\Catalog\ProductResource\Pages;
use Tasawk\Filament\Resources\Catalog\ProductResource\RelationManagers\OptionRelationManager;
use Tasawk\Models\Branch;
use Tasawk\Models\Catalog\Allergen;
use Tasawk\Models\Catalog\Category;
use Tasawk\Models\Catalog\Option;
use Tasawk\Models\Catalog\Product;
use Tasawk\Models\Content\Page;
use Tasawk\Traits\Filament\HasTranslationLabel;
use Filament\Forms;
use Filament\Forms\Components\SpatieMediaLibraryFileUpload;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Toggle;
use Filament\Forms\Form;
use Filament\Resources\Concerns\Translatable;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Actions\Action;
use Filament\Tables\Columns\IconColumn;
use Filament\Tables\Columns\SpatieMediaLibraryImageColumn;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;

class ProductResource extends Resource {
    use Translatable;
    use HasTranslationLabel;

    protected static ?int $navigationSort = 1;
    protected static ?string $model = Product::class;
    protected static ?string $navigationIcon = 'heroicon-o-building-storefront';

    public static function form(Form $form): Form {
        return $form
            ->schema([
                Forms\Components\Group::make()->schema([
                    Forms\Components\Section::make("general")->schema([
                        TextInput::make('title')
                            ->required()
                            ->translateLabel(),
                        Forms\Components\RichEditor::make('description')
                            ->required()
                            ->translateLabel(),
                        SpatieMediaLibraryFileUpload::make('image')
                            ->required(),

                        Toggle::make('status')->default(1)
                            ->onColor('success')
                            ->offColor('danger')
                            ->translateLabel(),
                    ]),
                    Forms\Components\Section::make("Calories")->schema([
                        TextInput::make('more_data.calories_from')
                            ->label(__('forms.fields.from'))
//                            ->required()
                            ->numeric()
                            ->default(0)
                            ->suffix(__('forms.suffixes.kcal')),


                        TextInput::make('more_data.calories_to')
                            ->label(__('forms.fields.to'))
                            ->numeric()
                            ->default(0)
                            ->suffix(__('forms.suffixes.kcal')),
                    ])->columns()
                ])->columnSpan(3),

                Forms\Components\Group::make()->schema([
                    Forms\Components\Section::make("Links")->schema([

                        Forms\Components\Select::make('categories')
                            ->multiple()
                            ->required()
                            ->relationship("categories", 'name')
                            ->getOptionLabelFromRecordUsing(fn(Category $record) => "{$record->getTranslation('name','en')} - {$record->getTranslation('name','ar')}")
                            ->searchable(['name->ar', 'name->en']),


                        Forms\Components\Select::make('allergens')
                            ->relationship("allergens", 'title')
                            ->getOptionLabelFromRecordUsing(fn(Allergen $record) => "{$record->getTranslation('title','en')} - {$record->getTranslation('title','ar')}")
                            ->multiple()
//                            ->required()
                            ->searchable(['title->ar', 'title->en']),

//                        Forms\Components\Select::make('options')
//                            ->multiple()
//                            ->required()
//                            ->relationship("options", 'name')
//                            ->getOptionLabelFromRecordUsing(fn(Option $record) => "{$record->getTranslation('name','en')} - {$record->getTranslation('name','ar')}")
//                            ->searchable(['name->ar', 'name->en']),

                        Forms\Components\Select::make('recommendations')
                            ->multiple()
                            ->relationship("recommendations", 'title')
                            ->getOptionLabelFromRecordUsing(fn(Product $record) => "{$record->getTranslation('title','en')} - {$record->getTranslation('title','ar')}")
                            ->searchable(['title->ar', 'title->en']),

                    ]),

                    Forms\Components\Section::make('Pricing')->schema([

                        TextInput::make('price')
                            ->required()
                            ->formatStateUsing(fn($record) => $record?->price?->formatByDecimal() ?? 0)
                            ->numeric()
                            ->suffix(__('forms.suffixes.sar')),

                        TextInput::make('sale_price')
                            ->required()
                            ->numeric()
                            ->lte('price')
                            ->formatStateUsing(fn($record) => $record?->sale_price?->formatByDecimal() ?? 0)
                            ->suffix(__('forms.suffixes.sar'))


                    ]),

                ])->columnSpan(2),
            ])->columns(5);
    }

    public static function table(Table $table): Table {
        return $table
            ->columns([
                TextColumn::make('id'),

                ImageColumn::make('avatar')
                    ->defaultImageUrl(fn(Product $record) => $record->getFirstMediaUrl())
                    ->width('100px'),

                TextColumn::make('title'),
                TextColumn::make('sale_price'),
                TextColumn::make('price'),
                IconColumn::make('status')
                    ->boolean()
                    ->action(
                        Action::make('Active')
                            ->label(fn(Product $record): string => $record->status ? __('panel.messages.deactivate') : __('panel.messages.activate'))
                            ->disabled(fn(Model $record): bool => !auth()->user()->can('update', $record))
                            ->requiresConfirmation()
                            ->action(fn(Product $record) => $record->toggleStatus())

                    )
            ])
            ->filters([
            SelectFilter::make('status')
                ->options(ModelStatus::class)


        ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make()
                    ->before(function (Tables\Actions\DeleteAction $action, Product $product) {
                        if ($product->orders()->count()) {
                            Notification::make()
                                ->warning()
                                ->title(__('panel.messages.warning'))
                                ->body(__('panel.messages.product_belong_to_many_orders', ['product' => $product->title]))
                                ->persistent()
                                ->send();
                            $action->cancel();
                        }
                    }),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])->checkIfRecordIsSelectableUsing(
                fn(Model $record): bool => !$record->orders()->count(),
            );
    }

    public static function getRelations(): array {
        return [
            OptionRelationManager::class
        ];
    }

    public static function getPages(): array {
        return [
            'index' => Pages\ListProducts::route('/'),
            'create' => Pages\CreateProduct::route('/create'),
            'edit' => Pages\EditProduct::route('/{record}/edit'),
        ];
    }

    public static function getNavigationGroup(): ?string {
        return __('menu.catalog');
    }

    public static function getNavigationBadge(): ?string {
        return static::getModel()::count();
    }
}
