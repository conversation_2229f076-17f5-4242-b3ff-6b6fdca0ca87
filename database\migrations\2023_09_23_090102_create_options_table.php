<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void {
        Schema::create('options', function (Blueprint $table) {
            $table->id();
            $table->text('name');
            $table->string('type');
            $table->timestamps();
        });
        Schema::create('option_values', function (Blueprint $table) {
            $table->id();
            $table->foreignId("option_id")
                ->constrained()
                ->cascadeOnDelete();
            $table->text('value');
            $table->integer('sort');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void {
        Schema::dropIfExists('option_values');
        Schema::dropIfExists('options');
    }
};
