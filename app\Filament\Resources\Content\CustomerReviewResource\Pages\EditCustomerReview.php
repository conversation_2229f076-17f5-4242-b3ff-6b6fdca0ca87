<?php

namespace Tasawk\Filament\Resources\Content\CustomerReviewResource\Pages;

use Filament\Actions;
use Filament\Actions\LocaleSwitcher;
use Filament\Resources\Pages\EditRecord;
use Tasawk\Filament\Resources\Content\CustomerReviewResource;

class EditCustomerReview extends EditRecord
{
    protected static string $resource = CustomerReviewResource::class;

    use EditRecord\Concerns\Translatable;

    protected function getHeaderActions(): array {
        return [
            LocaleSwitcher::make(),
        ];
    }
    protected function getRedirectUrl(): string {
        return $this->getResource()::getUrl("index");
    }
}
