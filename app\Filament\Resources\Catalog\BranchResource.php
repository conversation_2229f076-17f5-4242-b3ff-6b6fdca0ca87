<?php

namespace Tasawk\Filament\Resources\Catalog;

use <PERSON>zhanSalleh\FilamentShield\Contracts\HasShieldPermissions;
use Filament\Forms\Components\Textarea;
use Filament\Notifications\Notification;
use Filament\Tables\Actions\Action;
use Filament\Tables\Columns\IconColumn;
use Filament\Tables\Filters\Filter;
use Illuminate\Database\Eloquent\Model;
use MatanYadaev\EloquentSpatial\Objects\Geometry;
use MatanYadaev\EloquentSpatial\Objects\Point;
use Tasawk\Enum\OrderStatus;
use Tasawk\Enum\ReceiptMethods;
use Tasawk\Filament\Resources\Catalog\BranchResource\Pages;
use Tasawk\Filament\Resources\Catalog\BranchResource\RelationManagers\ProductsRelationManager;
use Tasawk\Lib\Options;
use Tasawk\Models\Branch;
use Tasawk\Models\Catalog\Option;
use Tasawk\Models\Catalog\Product;
use Tasawk\Models\Content\Page;
use Tasawk\Models\Manager;
use Tasawk\Models\Order;
use Tasawk\Models\Zone;
use Tasawk\Rules\KSAPhoneRule;
use Tasawk\Traits\Filament\HasTranslationLabel;
use Cheesegrits\FilamentGoogleMaps\Fields\Map;
use Closure;
use Filament\Forms;
use Filament\Forms\Components\Checkbox;
use Filament\Forms\Components\Group;
use Filament\Forms\Components\Hidden;
use Filament\Forms\Components\SpatieMediaLibraryFileUpload;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Toggle;
use Filament\Forms\Form;
use Filament\Resources\Concerns\Translatable;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Str;

class BranchResource extends Resource implements HasShieldPermissions {
    use Translatable;
    use HasTranslationLabel;

    protected static ?string $model = Branch::class;
    protected static ?string $navigationIcon = 'heroicon-o-map-pin';

    public static function form(Form $form): Form {

        return $form
            ->schema([
                Forms\Components\Group::make()->schema([
                    Forms\Components\Section::make('basic_information')
                        ->schema([
                            TextInput::make('name')
                                ->required()
                                ->minLength(3),

                            TextInput::make('phone')
                                ->required()
                                ->unique(ignoreRecord: true)
                                ->rules([new KSAPhoneRule]),

                            TextInput::make('email')
                                ->email()
                                ->unique(ignoreRecord: true)
                                ->required(),

                            SpatieMediaLibraryFileUpload::make('image')
                                ->image()
                                ->required(),


                        ]),
                    Forms\Components\Section::make('location')->schema([
                        Forms\Components\TextInput::make("address_name")
                            ->placeholder(__('forms.fields.address_name'))
                            ->autocomplete(false)
                            ->required(),


                        Forms\Components\Select::make('zone_id')
                            ->label(__('forms.fields.zone_name'))
                            ->afterStateUpdated(fn($set) => $set('boundaries', null))
                            ->live()
                            ->required()
                            ->relationship("zone", "name")
                            ->getOptionLabelFromRecordUsing(fn(Zone $record) => "{$record->getTranslation('name','en')} - {$record->getTranslation('name','ar')}")
                            ->searchable(),

                        Map::make('location')
                            ->live()
                            ->formatStateUsing(function ($record) {
                                if (!$record) return;
                                return [
                                    'lat' => Point::fromWkt($record->location)->latitude,
                                    'lng' => Point::fromWkt($record->location)->longitude,
                                ];
                            })
                            ->autocomplete('address_name')
                            ->debug()
//                            ->rules([
//                                function ($get) {
//                                    return function (string $attribute, $value, Closure $fail) use ($get) {
//
//                                        if (!$get('boundaries')) {
//                                            $fail(__('validation.boundaries_required', ['attribute' => __('forms.fields.boundaries')]));
//                                        }
//                                    };
//                                },
//                            ])
                            ->drawingField("boundaries")
                            ->defaultLocation([24.7136, 46.6753])
                            ->draggable()
                            ->clickable()


                        ,
                        Forms\Components\Hidden::make("boundaries")
                            ->live()
                            ->afterStateUpdated(function ($set, $get) {
                                $boundaries = $get('boundaries');
                                $features = json_decode($boundaries, true)['features'][0] ?? [];
                                $boundaries = json_encode(['type' => 'FeatureCollection', 'features' => [$features]]);
                                return $set('boundaries', $boundaries);
                            })
                            ->formatStateUsing(function ($record) {
                                if (!$record || !$record?->boundaries) return;
                                $json = json_decode(Geometry::fromJson($record?->boundaries?->toJson())->toFeatureCollectionJson() ?? '', true);
                                $json['features'][0]['properties'] = [
                                    'id' => Str::uuid(),
                                    'type' => 'polygon'
                                ];
                                return json_encode($json, JSON_NUMERIC_CHECK);
                            }),
//                            ->required(),
                    ])

                ])->columnSpan(3),

                Forms\Components\Group::make()->schema([
                    Forms\Components\Section::make('settings')->schema([
                        Forms\Components\Select::make('managers')
                            ->required()
                            ->relationship("managers")
                            ->options(function ($get, $record) {
                                return Manager::whereDoesntHave('branch')->orWhereIn('id', $record?->managers()?->pluck("manager_id") ?? [])->pluck('name', 'id')->toArray();
                            }),


                        Forms\Components\CheckboxList::make("receipt_methods")
                            ->required()
                            ->options(ReceiptMethods::class),

                        Toggle::make('maintenance_mode')
                            ->onColor('success')
                            ->offColor('danger'),

                        Toggle::make('heavy_load_mode')
                            ->onColor('success')
                            ->offColor('danger'),])->columns(1),
                    Forms\Components\Section::make('working_days')->schema(
                        static::WorkingDaysSchema())
                        ->columns(1)
                ])
                    ->columnSpan(2),])->columns(5);
    }

    public static function table(Table $table): Table {
        return $table
            ->columns([
                TextColumn::make('id')->searchable(),
                TextColumn::make('name')->searchable(),
                TextColumn::make('phone')->searchable(),
                TextColumn::make('email')->searchable(),
                TextColumn::make('zone.name')->searchable(),
                IconColumn::make('maintenance_mode')
                    ->boolean()
                    ->action(
                        Action::make('maintenance_mode')
                            ->label(fn(Branch $record): string => $record->maintenance_mode ? __('panel.messages.deactivate') : __('panel.messages.activate'))
                            ->disabled(fn(Model $record): bool => !auth()->user()->canAny(['update', 'update_statuses'], $record))
                            ->requiresConfirmation()
                            ->action(fn(Branch $record) => $record->update(['maintenance_mode' => !$record->maintenance_mode]))

                    ),
                IconColumn::make('heavy_load_mode')
                    ->boolean()
                    ->action(
                        Action::make('heavy_load_mode')
                            ->label(fn(Branch $record): string => $record->maintenance_mode ? __('panel.messages.deactivate') : __('panel.messages.activate'))
                            ->disabled(fn(Model $record): bool => !auth()->user()->canAny(['update', 'update_statuses'], $record))
                            ->requiresConfirmation()
                            ->action(fn(Branch $record) => $record->update(['heavy_load_mode' => !$record->heavy_load_mode]))

                    ),
            ])
            ->filters([
                Tables\Filters\TernaryFilter::make('maintenance_mode')->label(__('forms.fields.enable_maintenance_mode')),
                Tables\Filters\TernaryFilter::make('heavy_load_mode')->label(__('forms.fields.enable_heavy_load_mode')),
            ])
            ->actions([
                Tables\Actions\ViewAction::make(),


                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make()
                    ->before(function (Tables\Actions\DeleteAction $action, Branch $branch) {
                        if ($branch->orders()->count()) {
                            Notification::make()
                                ->warning()
                                ->title(__('panel.messages.warning'))
                                ->body(__('panel.messages.branch_has_orders', ['branch' => $branch->name]))
                                ->persistent()
                                ->send();
                            $action->cancel();
                        }
                    }),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make()

                ]),
            ])->checkIfRecordIsSelectableUsing(
                fn(Model $record): bool => !$record->orders()->count(),
            );
    }

    public
    static function getRelations(): array {
        return [
            ProductsRelationManager::class
        ];
    }

    public static function getPages(): array {
        return [
            'index' => Pages\ListBranches::route('/'),
            'create' => Pages\CreateBranch::route('/create'),
            'edit' => Pages\EditBranch::route('/{record}/edit'),
            'view' => Pages\ViewBranch::route('/{record}/view'),

        ];
    }

    public static function canCreate(): bool {
        return true;
    }

    static public function WorkingDaysSchema() {
        $schema = [];
        foreach (['saturday', 'sunday', 'monday', 'tuesday', 'wednesday', 'thursday', 'friday'] as $day) {

            $schema [] = Group::make([
                Checkbox::make("$day.status")->label(__("forms.fields.weekdays.$day")),
                Hidden::make("$day.day_name")
                    ->default(fn() => $day)
                    ->formatStateUsing(fn() => $day),
                TextInput::make("$day.from")->type('time')->label('')->default("00:00"),
                TextInput::make("$day.to")->type('time')->label('')->default("23:59"),
            ])->columns(3);
        }
        return [
            Forms\Components\Repeater::make('working_days')
                ->formatStateUsing(fn($record) => $record->working_days??[])
//                ->label(__('forms.fields.morning_shift'))
                ->schema($schema)
                ->reorderable(false)
                ->deletable(true)
                ->rules([
                    function () {
                        return function (string $attribute, $value, Closure $fail) {
                            if (!collect($value)->filter(fn($slot) => collect($slot)->filter(fn($day) => $day['status'])->count())->count()) {
                                $fail(__('validation.at_lest_one_day'));
                            }
                        };
                    },
                ])
                ->minItems(1)
                ->maxItems(3),
        ];
    }

    public static function getNavigationBadge(): ?string {
        return static::getModel()::count();
    }

    public static function getNavigationGroup(): ?string {
        return __('menu.catalog');
    }

    public static function getGloballySearchableAttributes(): array {
        return ['name->ar', 'name->en', 'phone', 'email'];
    }

    public static function getGlobalSearchResultDetails(Model $record): array {
        return [
            __('forms.fields.email') => $record->email,
            __('forms.fields.phone') => $record->phone,
        ];
    }

    public static function getPermissionPrefixes(): array {
        return [
            'view_any',
            'view',
            'update_statuses',
            'update',
            'delete',
            'delete_any',
        ];
    }
}
