<?php

namespace Tasawk\Filament\Resources\Catalog\BranchResource\RelationManagers;

use Filament\Notifications\Notification;
use Illuminate\Database\Eloquent\Builder;
use Tasawk\Models\Branch;
use Tasawk\Models\Catalog\Allergen;
use Tasawk\Models\Catalog\Branch\Inventory;
use Tasawk\Models\Catalog\Option;
use Tasawk\Models\Catalog\Product;
use Tasawk\Models\Catalog\Value;
use Filament\Forms;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Toggle;
use Filament\Forms\Components\Wizard\Step;
use Filament\Forms\Form;
use Filament\Forms\Get;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Actions\Action;
use Filament\Tables\Columns\IconColumn;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Collection;

class ProductsRelationManager extends RelationManager {
    private $product = null;
    protected bool $allowsDuplicates = false;
    protected static string $relationship = 'inventories';

    public function form(Form $form): Form {
        return $form
            ->schema([])
            ->columns(1);
    }

    public function table(Table $table): Table {
        return $table
            ->heading(__('sections.products'))
            ->columns([
                Tables\Columns\TextColumn::make('id'),
                Tables\Columns\TextColumn::make('product.title'),
                Tables\Columns\TextColumn::make('from')->time("h:i a"),
                Tables\Columns\TextColumn::make('to')->time("h:i a"),
                IconColumn::make('status')
                    ->boolean()
                    ->action(
                        Action::make('Active')
                            ->label(fn(Inventory $record): string => $record->status ? __('panel.messages.deactivate') : __('panel.messages.activate'))
                            ->disabled(fn(Model $record): bool => !auth()->user()->canAny(['update','update_statuses'], $record->branch))
                            ->requiresConfirmation()
                            ->action(fn(Inventory $record) => $record->toggleStatus())

                    )
            ])
            ->filters([
                //
            ])
            ->headerActions([
                Tables\Actions\CreateAction::make()->form($this->wizardSchema()),
                Action::make('add_multiple')->label(__('forms.actions.add_multiple'))
                    ->form($this->wizardSchema(true))
                    ->action(function ($record, array $data) {
                        foreach ($data['ids'] as $product_id) {
                            $this->getOwnerRecord()->inventories()->create([
                                'product_id' => $product_id,
                                'from' => $data['from'],
                                'to' => $data['to'],
                                'status' => $data['status'],
                            ]);
                        }
                    }),

            ])
            ->actions([
                Tables\Actions\EditAction::make()->form($this->wizardSchema()),

                Action::make("options")->label(__('sections.options'))
                    ->form(fn(Model $record) => $this->optionsSchema($record))
                    ->action(function (Inventory $record, array $data) {
                        foreach ($data['options'] ?? [] as $option) {

                            $inventoryOption = $record->options()->updateOrCreate(['option_id' => $option['option_id']], ['status' => $option['status']]);
                            foreach ($option['values'] as $value) {
                                $inventoryOption->values()->updateOrCreate(['value_id' => $value['value_id']], ['status' => $value['status']]);
                            }

                        }

                    })
                ,
                Tables\Actions\DeleteAction::make()->before(function (Tables\Actions\DeleteAction $action, Inventory $inventory) {
                    if ($inventory?->product?->orders()->count()) {
                        Notification::make()
                            ->warning()
                            ->title(__('panel.messages.warning'))
                            ->body(__('panel.messages.product_belong_to_many_order', ['product' => $inventory->product->title]))
                            ->persistent()
                            ->send();
                        $action->cancel();
                    }
                }),

            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public function wizardSchema($multiple=false): array {
        $field=$multiple?'ids':'product_id';
        return [

            Forms\Components\Hidden::make('recordId'),
            Forms\Components\Select::make($field)
                ->multiple($multiple)
                ->live()
                ->label(__("forms.fields.product_name"))
                ->options(Product::pluck('title', 'id'))
                ->disableOptionWhen(fn(string $value): bool => in_array($value, $this->getOwnerRecord()->inventories->pluck('product_id')->toArray()))
                ->required(),
            TextInput::make("from")
                ->nullable()
                ->type('time'),

            TextInput::make("to")
                ->nullable()
                ->after('from')
                ->type('time'),

            Toggle::make('status')
                ->onColor('success')
                ->offColor('danger')
                ->default(1),

//            Forms\Components\Fieldset::make(__('sections.options'))
//                ->schema($this->optionsSchema())

//                ->relationship('options')

        ];
    }

    public function optionsSchema($record) {
        $inputs = [];
        if (!$record->product->options->count())
            return [
                Forms\Components\Placeholder::make("nodata")->label(__('panel.messages.no_data_found_product_options'))

            ];
        foreach ($record->product->options as $index => $option) {
            $inputs = [...$inputs, ...[
                Forms\Components\Grid::make()->schema([
                    Forms\Components\Placeholder::make("options.$index.option_id")->label($index + 1 . " - " . $option->option->name),
                    Forms\Components\Hidden::make("options.$index.option_id")->default($option->id),


                    Toggle::make("options.$index.status")
                        ->formatStateUsing(fn() => $record->options->where('option_id', $option->id)->first()?->status ?? 0)
                        ->label(__('forms.fields.status'))
                        ->onColor('success')
                        ->offColor('danger'),
                ])


            ]];
            foreach ($option->values as $valueIndex => $value) {

                $inputs = [...$inputs, ...[
                    Forms\Components\Grid::make()->schema([
                        Forms\Components\Placeholder::make("options.$index.values.$valueIndex.value_id")->label($value->value->value),
                        Forms\Components\Hidden::make("options.$index.values.$valueIndex.value_id")->default($value->id),
                        Toggle::make("options.$index.values.$valueIndex.status")
                            ->formatStateUsing(fn() => $record->options->where('option_id', $option->id)->first()?->values->where('value_id', $value->id)->first()?->status ?? 0)
                            ->label(__('forms.fields.status'))
                            ->onColor('success')
                            ->offColor('danger')
                            ->default(0),
                    ])
                ]];
            }
        };
        return $inputs;
    }

    /**
     * @return string|null
     */
    public static function getModelLabel(): ?string {
        return __('sections.product');
    }
}
