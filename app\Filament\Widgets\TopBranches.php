<?php

namespace Tasawk\Filament\Widgets;

use <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>\FilamentShield\Traits\HasWidgetShield;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;
use Filament\Widgets\TableWidget as BaseWidget;
use Tasawk\Models\Branch;

class TopBranches extends BaseWidget {
    use HasWidgetShield;
    protected static ?int $sort = 8;
    public function table(Table $table): Table {
        return $table
            ->heading(__('sections.top_branches'))
            ->description(__('sections.top_branches_description'))
            ->query(
                Branch::whereHas('orders')->withCount('orders')->orderBy("orders_count",'desc')->limit(5),
            )
            ->columns([

                TextColumn::make('index')->rowIndex()->toggleable(false),
                TextColumn::make('name')->toggleable(false),
                TextColumn::make('orders_count')->toggleable(false),
            ]);
    }
    public function getTableHeading(): ?string {
        return __('sections.top_branches');
    }
}
