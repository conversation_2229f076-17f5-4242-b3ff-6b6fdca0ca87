<?php

namespace Tasawk\Filament\Resources\Catalog\OptionResource\Pages;

use Tasawk\Enum\Catalog\OptionTypes;
use Tasawk\Filament\Resources\Catalog\OptionResource;
use Tasawk\Lib\Options;
use Filament\Actions;
use Filament\Facades\Filament;
use Filament\Forms\Components\Hidden;
use Filament\Forms\Components\Repeater;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Toggle;
use Filament\Forms\Form;
use Filament\Resources\Pages\CreateRecord;
use Filament\Resources\Pages\CreateRecord\Concerns\Translatable;
use Filament\SpatieLaravelTranslatablePlugin;

class CreateOption extends CreateRecord {
    use Translatable,OptionResource\Schema\OptionSchema;

    protected static string $resource = OptionResource::class;

    protected function getHeaderActions(): array {

        return [
            Actions\LocaleSwitcher::make(),
        ];
    }
    protected function getRedirectUrl(): string {
        return $this->getResource()::getUrl("index");
    }
    public function form(Form $form): Form {
        return $form->schema($this->schema());
    }


}
