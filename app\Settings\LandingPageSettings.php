<?php

namespace Tasawk\Settings;

use Carbon\Carbon;
use <PERSON><PERSON>\LaravelSettings\Settings;
use Tasawk\Api\Facade\Api;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;

class LandingPageSettings extends Settings {

    public array $about_description = [];
    public array $about_features = [];
    public array $features = [];
    public array $our_features_description = [];
    public array $app_screen = [];
    public array $clients_reviews = [];
    public array $faq = [];
    public array $contact_us = [];
    public string $logo = '';
    public string $our_features_image = '';

    public array $bas_info = [];
    public static function group(): string {
        return 'landing';
    }
}
