<?php

namespace Tasawk\Console\Commands;

use Http;
use Illuminate\Console\Command;
use Illuminate\Support\Str;
use Tasawk\Enum\ReceiptMethods;
use Tasawk\Models\Branch;
use Tasawk\Models\Catalog\Product;
use Tasawk\Models\Customer;
use Tasawk\Models\User;
use Tasawk\Models\Zone;

class ImportKufaDataCommand extends Command {
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:import-kufa-data-command';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Command description';

    /**
     * Execute the console command.
     */
    public function handle() {


        $this->importProducts();
        $this->importBranches();
        $this->importUser();

    }

    public function importUser(): void {
        $this->warn('start importing users data');
        $users = Http::get('https://manager.kufa.sa/api/customers')->json();
        foreach ($users as $user) {
            $user = User::create([
                'name' => $user['name'],
                'phone' => $user['phone'],
                'email' => $user['email'],
                'password' => $user['password'],
                'active' => 1
            ]);
            $user->assignRole(Customer::ROLE);
        }
        $this->info('users data imported');

    }

    public function importProducts(): void {
        $this->warn('start importing products data');
        $products = Http::get('https://manager.kufa.sa/api/items')->json();

        foreach ($products as $product) {
            $ar = collect($product['translations'])->where('locale', 'ar')->first();
            $en = collect($product['translations'])->where('locale', 'en')->first();

            $_product = Product::create([
                'title' => [
                    'ar' => $ar['title'],
                    'en' => $en['title']
                ],
                'description' => [
                    'ar' => $ar['description'],
                    'en' => $en['description']
                ],
                'price' => $product['price'],
                'sale_price' => 0,
                'status' => 1
            ]);

            if (Str::of($product['image'])->startsWith("http")) {
                try {
                    $image = Str::of($product['image'])->replaceLast("//", "/")->replace('kufa.sa', 'manager.kufa.sa');

                    $_product->addMediaFromUrl($image)->toMediaCollection();
                } catch (\Exception $exception) {
                }
            }

        }
        $this->info('products data imported');

    }

    public function importBranches(): void {
        $this->warn('start importing branches data');
        $branches = Http::get('https://manager.kufa.sa/api/branches')->json();

        foreach ($branches as $branch) {
            $ar = collect($branch['translations'])->where('locale', 'ar')->first();
            $en = collect($branch['translations'])->where('locale', 'en')->first();
            $_branch = Branch::create([
                'name' => ['ar' => $ar['name'], 'en' => $en['name']],
                'phone' => $branch['phone'],
                'email' => $branch['email'],
                'address_name' => $ar['address'],
                'location' => $branch['map_location'],
                'receipt_methods' => ReceiptMethods::DELIVERY,
                'zone_id' => Zone::first()->id,
                'maintenance_mode' => false,
                'heavy_load_mode' => false,
                'working_days' => [],
            ]);
            try {
                $image = Str::of($branch['image'])->replaceLast("//", "/")->replace('kufa.sa', 'manager.kufa.sa');

                $_branch->addMediaFromUrl($image)->toMediaCollection();

            } catch (\Exception $exception) {
            }
        }
        $this->info('branches data imported');
    }
}
