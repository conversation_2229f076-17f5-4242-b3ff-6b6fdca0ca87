<?php

namespace Tasawk\Models\Catalog\Branch;

use Tasawk\Models\Catalog\Branch\BranchOption;
use Tasawk\Models\Catalog\Branch\BranchProduct;
use Tasawk\Models\Catalog\Branch\BranchValue;
use Tasawk\Models\Catalog\Branch\Inventory;
use Tasawk\Models\Catalog\Option;
use Tasawk\Models\Catalog\Product;
use Tasawk\Models\Catalog\Value;
use Tasawk\Models\User;
use Tasawk\Models\Zone;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;
use Spatie\Translatable\HasTranslations;

class BranchAccount extends Model implements HasMedia {
    use HasFactory, HasTranslations, InteractsWithMedia;

    protected $table = 'branch_account';
    protected $fillable = [
        'user_id',
    ];

    public function data() {
        return $this->hasMany(User::class, 'account_id');
    }
}
