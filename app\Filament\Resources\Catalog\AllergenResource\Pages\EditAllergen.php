<?php

namespace Tasawk\Filament\Resources\Catalog\AllergenResource\Pages;

use Tasawk\Filament\Resources\Catalog\AllergenResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

class EditAllergen extends EditRecord {
    protected static string $resource = AllergenResource::class;
    use EditRecord\Concerns\Translatable;

    protected function getHeaderActions(): array {
        return [
            Actions\LocaleSwitcher::make(),
        ];
    }
    protected function getRedirectUrl(): string {
        return $this->getResource()::getUrl("index");
    }
}
