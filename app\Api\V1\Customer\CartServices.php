<?php

namespace Tasawk\Api\V1\Customer;


use Api;
use Illuminate\Support\Facades\Notification;
use Tasawk\Actions\Customer\BuildCartInstanceAction;
use Tasawk\Actions\Customer\CreateOrderAction;
use Tasawk\Actions\Customer\PayOrderAction;
use Tasawk\Api\Core;
use Tasawk\Http\Requests\Api\Customer\Order\CartCheckoutRequest;
use Tasawk\Http\Requests\Api\Customer\Order\CartDetailsRequest;
use Tasawk\Http\Resources\Api\Customer\Cart\CartResource;
use Tasawk\Http\Resources\Api\Customer\Orders\OrdersResource;
use Tasawk\Services\MyFatoorahPaymentService;

class CartServices {
    public function details(CartDetailsRequest $request) {
        $cart = BuildCartInstanceAction::run();
        return Api::isOk("cart Details", CartResource::make($cart));
    }

    public function checkout(CartCheckoutRequest $request, MyFatoorahPaymentService $paymentService): Core {

        $cart = BuildCartInstanceAction::run();
        $order = CreateOrderAction::run($cart->getTotal(), ...$request->only('payment_gateway','address_id', 'notes', 'receipt_method', 'exceed_limit', 'is_heavy_load_time'));

        $cart->saveItemsToOrder($order->id);
        PayOrderAction::run($order, $paymentService);

        return Api::isOk("Order Details", OrdersResource::make($order));

    }
}
