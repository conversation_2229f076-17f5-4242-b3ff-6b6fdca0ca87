<?php

namespace Tasawk\Filament\Resources\Employees\ManagerResource\Pages;

use Tasawk\Filament\Resources\Employees\ManagerResource;
use Filament\Actions;
use Filament\Resources\Pages\CreateRecord;

class CreateManager extends CreateRecord
{
    protected static string $resource = ManagerResource::class;

    protected function handleRecordCreation(array $data): \Illuminate\Database\Eloquent\Model {
        dd($data);
    }
}
