<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Tasawk\Models\Zone;
use Tasawk\Models\Branch;
use Tasawk\Models\User;
use Tasawk\Models\Manager;
use Tasawk\Models\Operator;
use MatanYadaev\EloquentSpatial\Objects\Point;
use MatanYadaev\EloquentSpatial\Objects\Polygon;
use MatanYadaev\EloquentSpatial\Objects\LineString;

class BranchDataSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create zones first
        $zones = [
            [
                'name' => ['en' => 'Central District', 'ar' => 'المنطقة المركزية'],
                'status' => true,
            ],
            [
                'name' => ['en' => 'North District', 'ar' => 'المنطقة الشمالية'],
                'status' => true,
            ],
            [
                'name' => ['en' => 'South District', 'ar' => 'المنطقة الجنوبية'],
                'status' => true,
            ],
            [
                'name' => ['en' => 'East District', 'ar' => 'المنطقة الشرقية'],
                'status' => true,
            ],
            [
                'name' => ['en' => 'West District', 'ar' => 'المنطقة الغربية'],
                'status' => true,
            ],
        ];

        foreach ($zones as $zoneData) {
            Zone::create($zoneData);
        }

        // Create manager users
        $managers = [
            [
                'name' => 'Ahmed Al-Mansouri',
                'email' => '<EMAIL>',
                'phone' => '+96512345001',
                'password' => Hash::make('password123'),
                'active' => true,
            ],
            [
                'name' => 'Fatima Al-Zahra',
                'email' => '<EMAIL>',
                'phone' => '+96512345002',
                'password' => Hash::make('password123'),
                'active' => true,
            ],
            [
                'name' => 'Mohammed Al-Rashid',
                'email' => '<EMAIL>',
                'phone' => '+96512345003',
                'password' => Hash::make('password123'),
                'active' => true,
            ],
        ];

        $managerIds = [];
        foreach ($managers as $managerData) {
            $manager = Manager::create($managerData);
            $managerIds[] = $manager->id;
        }

        // Create operator users
        $operators = [
            [
                'name' => 'Ali Hassan',
                'email' => '<EMAIL>',
                'phone' => '+96512345101',
                'password' => Hash::make('password123'),
                'active' => true,
            ],
            [
                'name' => 'Noor Abdullah',
                'email' => '<EMAIL>',
                'phone' => '+96512345102',
                'password' => Hash::make('password123'),
                'active' => true,
            ],
            [
                'name' => 'Omar Al-Sabah',
                'email' => '<EMAIL>',
                'phone' => '+96512345103',
                'password' => Hash::make('password123'),
                'active' => true,
            ],
            [
                'name' => 'Layla Al-Mutairi',
                'email' => '<EMAIL>',
                'phone' => '+96512345104',
                'password' => Hash::make('password123'),
                'active' => true,
            ],
            [
                'name' => 'Khalid Al-Ahmad',
                'email' => '<EMAIL>',
                'phone' => '+96512345105',
                'password' => Hash::make('password123'),
                'active' => true,
            ],
        ];

        $operatorIds = [];
        foreach ($operators as $operatorData) {
            $operator = Operator::create($operatorData);
            $operatorIds[] = $operator->id;
        }

        // Create branches
        $branches = [
            [
                'name' => ['en' => 'Kufa Central Branch', 'ar' => 'فرع الكوفة المركزي'],
                'phone' => '+***********',
                'email' => '<EMAIL>',
                'address_name' => 'Central Business District, Kuwait City',
                'zone_id' => 1,

                'location' => ['lat' => 29.3759, 'lng' => 47.9774], // Kuwait City coordinates
                'boundaries' => new Polygon([
                    new LineString([
                        new Point(29.3700, 47.9700),
                        new Point(29.3800, 47.9700),
                        new Point(29.3800, 47.9800),
                        new Point(29.3700, 47.9800),
                        new Point(29.3700, 47.9700),
                    ])
                ]),
                'receipt_methods' => ['delivery', 'pickup'],
                'maintenance_mode' => false,
                'heavy_load_mode' => false,
                'working_days' => [
                    'sunday' => ['from' => '08:00', 'to' => '22:00', 'active' => true],
                    'monday' => ['from' => '08:00', 'to' => '22:00', 'active' => true],
                    'tuesday' => ['from' => '08:00', 'to' => '22:00', 'active' => true],
                    'wednesday' => ['from' => '08:00', 'to' => '22:00', 'active' => true],
                    'thursday' => ['from' => '08:00', 'to' => '22:00', 'active' => true],
                    'friday' => ['from' => '14:00', 'to' => '22:00', 'active' => true],
                    'saturday' => ['from' => '08:00', 'to' => '22:00', 'active' => true],
                ],
            ],
            [
                'name' => ['en' => 'Kufa North Branch', 'ar' => 'فرع الكوفة الشمالي'],
                'phone' => '+96512340002',
                'email' => '<EMAIL>',
                'address_name' => 'Jahra District, North Kuwait',
                'zone_id' => 2,

                'location' => ['lat' => 29.3375, 'lng' => 47.6581], // Jahra coordinates
                'boundaries' => new Polygon([
                    new LineString([
                        new Point(29.3300, 47.6500),
                        new Point(29.3450, 47.6500),
                        new Point(29.3450, 47.6650),
                        new Point(29.3300, 47.6650),
                        new Point(29.3300, 47.6500),
                    ])
                ]),
                'receipt_methods' => ['delivery', 'pickup'],
                'maintenance_mode' => false,
                'heavy_load_mode' => false,
                'working_days' => [
                    'sunday' => ['from' => '09:00', 'to' => '21:00', 'active' => true],
                    'monday' => ['from' => '09:00', 'to' => '21:00', 'active' => true],
                    'tuesday' => ['from' => '09:00', 'to' => '21:00', 'active' => true],
                    'wednesday' => ['from' => '09:00', 'to' => '21:00', 'active' => true],
                    'thursday' => ['from' => '09:00', 'to' => '21:00', 'active' => true],
                    'friday' => ['from' => '15:00', 'to' => '21:00', 'active' => true],
                    'saturday' => ['from' => '09:00', 'to' => '21:00', 'active' => true],
                ],
            ],
            [
                'name' => ['en' => 'Kufa South Branch', 'ar' => 'فرع الكوفة الجنوبي'],
                'phone' => '+96512340003',
                'email' => '<EMAIL>',
                'address_name' => 'Ahmadi District, South Kuwait',
                'zone_id' => 3,

                'location' => ['lat' => 29.0769, 'lng' => 48.0810], // Ahmadi coordinates
                'boundaries' => new Polygon([
                    new LineString([
                        new Point(29.0700, 48.0750),
                        new Point(29.0850, 48.0750),
                        new Point(29.0850, 48.0900),
                        new Point(29.0700, 48.0900),
                        new Point(29.0700, 48.0750),
                    ])
                ]),
                'receipt_methods' => ['delivery', 'pickup'],
                'maintenance_mode' => false,
                'heavy_load_mode' => false,
                'working_days' => [
                    'sunday' => ['from' => '08:30', 'to' => '21:30', 'active' => true],
                    'monday' => ['from' => '08:30', 'to' => '21:30', 'active' => true],
                    'tuesday' => ['from' => '08:30', 'to' => '21:30', 'active' => true],
                    'wednesday' => ['from' => '08:30', 'to' => '21:30', 'active' => true],
                    'thursday' => ['from' => '08:30', 'to' => '21:30', 'active' => true],
                    'friday' => ['from' => '14:30', 'to' => '21:30', 'active' => true],
                    'saturday' => ['from' => '08:30', 'to' => '21:30', 'active' => true],
                ],
            ],
        ];

        $branchIds = [];
        foreach ($branches as $branchData) {
            $branch = Branch::create($branchData);
            $branchIds[] = $branch->id;
        }

        // Create branch_manager relationships
        $branchManagerRelations = [
            ['branch_id' => $branchIds[0], 'manager_id' => $managerIds[0]],
            ['branch_id' => $branchIds[1], 'manager_id' => $managerIds[1]],
            ['branch_id' => $branchIds[2], 'manager_id' => $managerIds[2]],
        ];

        foreach ($branchManagerRelations as $relation) {
            DB::table('branch_manager')->insert($relation);
        }

        // Create branch_account relationships (operators to branches)
        $branchAccountRelations = [
            ['branch_id' => $branchIds[0], 'account_id' => $operatorIds[0]],
            ['branch_id' => $branchIds[0], 'account_id' => $operatorIds[1]],
            ['branch_id' => $branchIds[1], 'account_id' => $operatorIds[2]],
            ['branch_id' => $branchIds[2], 'account_id' => $operatorIds[3]],
            ['branch_id' => $branchIds[2], 'account_id' => $operatorIds[4]],
        ];

        foreach ($branchAccountRelations as $relation) {
            DB::table('branch_account')->insert($relation);
        }

        $this->command->info('Branch data seeded successfully!');
        $this->command->info('Created:');
        $this->command->info('- 5 zones');
        $this->command->info('- 3 managers');
        $this->command->info('- 5 operators');
        $this->command->info('- 3 branches');
        $this->command->info('- Branch-manager relationships');
        $this->command->info('- Branch-account relationships');
    }
}
