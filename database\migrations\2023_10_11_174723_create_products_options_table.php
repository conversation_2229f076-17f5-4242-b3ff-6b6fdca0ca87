<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void {

        Schema::create('products_options', function (Blueprint $table) {
            $table->id();
            $table->foreignId("product_id");
            $table->foreignId("option_id");
            $table->boolean("status")->default(1);
            $table->boolean("required");
            $table->timestamps();

        });
        Schema::create('products_options_values', function (Blueprint $table) {
            $table->id();
            $table->foreignId("product_option_id");
            $table->foreignId("value_id");
            $table->boolean("status")->default(1);
            $table->float('price', 8, 3);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void {
        Schema::dropIfExists('products_options');
        Schema::dropIfExists('products_options_values');
    }
};
