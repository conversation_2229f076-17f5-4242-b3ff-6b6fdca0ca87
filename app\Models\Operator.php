<?php

namespace Tasawk\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;
use Tasawk\Models\Catalog\Branch\BranchAccount;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Laravel\Sanctum\HasApiTokens;
use Spatie\Permission\Traits\HasRoles;

class Operator extends User {
    protected $table = "users";
    protected string $guard_name = 'web';

    public function getMorphClass(): string {
        return User::class;
    }

    protected static function booted() {
        parent::booted();
        static::creating(fn($model) => $model->assignRole('operator'));
        static::addGlobalScope("operator", function ($builder) {
            $builder->whereHas("roles", fn($q) => $q->where('name', 'operator'));
        });
    }

    public function branch(): BelongsToMany {
        return $this->belongsToMany(Branch::class, BranchAccount::class, 'account_id');
    }


    public function mainBranch() {
        return $this->branch->first();
    }
}
