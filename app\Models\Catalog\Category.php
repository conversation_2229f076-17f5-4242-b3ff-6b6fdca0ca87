<?php

namespace Tasawk\Models\Catalog;

use <PERSON><PERSON>\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;
use Tasawk\Traits\Publishable;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Spatie\Translatable\HasTranslations;

class Category extends Model implements HasMedia {
    use HasTranslations, Publishable, InteractsWithMedia;

    protected $fillable = ['name', 'status', 'parent_id','sort'];
    public $translatable = ['name'];
    use HasFactory;

    public function scopeParent($builder) {
        return $builder->where('parent_id', null);
    }

    public function scopeChildren($builder) {
        return $builder->where('parent_id', "!=", null);
    }

    public function children() {
        return $this->hasMany(Category::class, 'parent_id');
    }

    public function products(): \Illuminate\Database\Eloquent\Relations\BelongsToMany {
        return $this->belongsToMany(Product::class);
    }

}
