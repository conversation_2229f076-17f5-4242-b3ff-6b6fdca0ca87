{"name": "laravel/laravel", "type": "project", "description": "The skeleton application for the Laravel framework.", "keywords": ["laravel", "framework"], "license": "MIT", "require": {"php": "^8.1", "ext-intl": "*", "alexpechkarev/geometry-library": "1.0.4", "bezhansalleh/filament-language-switch": "^3.1", "bezhansalleh/filament-shield": "^3.0@beta", "carlos-meneses/laravel-mpdf": "^2.1", "cheesegrits/filament-google-maps": "^3.0", "christiankuri/laravel-favorite": "^1.4", "cknow/laravel-money": "^7.2", "darryldecode/cart": "^4.2", "filament/filament": "^3.2", "filament/infolists": "^3.2", "filament/spatie-laravel-google-fonts-plugin": "^3.2", "filament/spatie-laravel-media-library-plugin": "^3.2", "filament/spatie-laravel-settings-plugin": "^3.2", "filament/spatie-laravel-translatable-plugin": "^3.2", "flowframe/laravel-trend": "^0.1.5", "google/apiclient": "^2.18", "guzzlehttp/guzzle": "^7.2", "hasnayeen/themes": "^3.0", "laravel/framework": "^10.1.0", "laravel/sanctum": "^3.2", "laravel/telescope": "^5.5", "laravel/tinker": "^2.8", "lorisleiva/laravel-actions": "^2.7", "matanyadaev/laravel-eloquent-spatial": "^3.2", "mcamara/laravel-localization": "*", "mpdf/mpdf": "^8.2", "myfatoorah/laravel-package": "2.1", "pxlrbt/filament-excel": "^2.1", "salla/zatca": "^2.0", "spatie/laravel-options": "^1.1", "stijnvanouplines/blade-country-flags": "^1.0", "tasawk/api": "1.0.16", "tecnickcom/tc-lib-pdf": "^8.0", "tightenco/ziggy": "^1.6"}, "require-dev": {"barryvdh/laravel-ide-helper": "^2.13", "fakerphp/faker": "^1.9.1", "laravel/pint": "^1.0", "laravel/sail": "^1.18", "mockery/mockery": "^1.4.4", "nunomaduro/collision": "^7.0", "phpunit/phpunit": "^10.1", "spatie/laravel-ignition": "^2.0"}, "autoload": {"psr-4": {"Tasawk\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/"}}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi", "@php artisan filament:upgrade"], "post-update-cmd": ["@php artisan vendor:publish --tag=laravel-assets --ansi --force"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi"]}, "extra": {"laravel": {"dont-discover": []}}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "allow-plugins": {"pestphp/pest-plugin": true, "php-http/discovery": true}}, "minimum-stability": "stable", "prefer-stable": true, "repositories": {"producation": {"type": "composer", "url": "https://gitlab.com/api/v4/group/12160558/-/packages/composer/packages.json"}}}