<?php

namespace Tasawk\Filament\Resources\Catalog\ProductResource\Pages;

use Tasawk\Filament\Resources\Catalog\ProductResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;
use Filament\Resources\Pages\EditRecord\Concerns\Translatable;

class EditProduct extends EditRecord {
    use Translatable;

    protected static string $resource = ProductResource::class;

    protected function getHeaderActions(): array {
        return [
            Actions\LocaleSwitcher::make(),
        ];
    }
}
