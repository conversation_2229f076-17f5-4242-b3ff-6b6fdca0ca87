<?php

namespace Tasawk\Actions\Customer;

use Darrylde<PERSON>\Cart\CartCondition;
use GeometryLibrary\SphericalUtil;
use Lorisleiva\Actions\Concerns\AsAction;
use Tasawk\Enum\ReceiptMethods;
use Tasawk\Lib\Cart;
use Tasawk\Lib\Utils;
use Tasawk\Models\AddressBook;
use Tasawk\Models\Catalog\Product;


class BuildCartInstanceAction {
    use AsAction;

    protected $data = [];

    public function handle() {
        /**
         *
         * @var Cart $cart
         * */
        $cart = app('cart');
        $cart->clear();
        foreach (request()->collect('products') as $_product) {
            $product = Product::find($_product['id']);
            $conditions = [];
            $attributes = [];
            if (isset($_product['options'])&& is_array($_product['options'])) {
                foreach ($_product['options'] ?? [] as $option) {
                    $inventoryOption = $product->branchInventory()->options()->where('id', $option['id'])->first();
                    $inventoryOptionValue = $inventoryOption->values()->where('id', $option['value_id'])->first();
                    $attributes ['options'][] = ['id' => $inventoryOption->id, 'name' => $inventoryOption->option->option->getTranslations('name'), 'value' => $inventoryOptionValue->value->value->getTranslations('value'), 'value_id' => $inventoryOptionValue->id, 'price' => $inventoryOptionValue->value->price->formatByDecimal()];

                    $conditions[] = new CartCondition([
                        'name' => $inventoryOption->option->option->name,
                        'type' => 'option',
                        'value' => "+{$inventoryOptionValue->value->price->formatByDecimal()}",
                    ]);
                }
            }
            $cart->applyItem($product, $_product['quantity'], $attributes, $conditions);

        }

        $cart->applyTaxes();
        if (request()->get('receipt_method') == 'delivery') {
            $branchLocation = Utils::getBranchLocation();
            $customerLocation = AddressBook::find(request()->get('address_id'))->map_location;
            $distance = SphericalUtil::computeDistanceBetween(
                ['lat' => $branchLocation->latitude, 'lng' => $branchLocation->longitude], //from array [lat, lng]
                $customerLocation);

            $cart->applyDeliveryService(round($distance / 1000));
        }
        if (request()->get('receipt_method') == ReceiptMethods::TAKEAWAY->value) {

            $cart->applyTakeawayDiscount();
        }
        return $cart;
    }


}
