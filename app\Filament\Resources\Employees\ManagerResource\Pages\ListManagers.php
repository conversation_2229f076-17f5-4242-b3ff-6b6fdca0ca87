<?php

namespace Tasawk\Filament\Resources\Employees\ManagerResource\Pages;

use Tasawk\Filament\Resources\Employees\ManagerResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListManagers extends ListRecords
{
    protected static string $resource = ManagerResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
