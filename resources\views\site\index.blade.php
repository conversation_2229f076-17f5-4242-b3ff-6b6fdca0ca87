@extends('site.layouts.app')
@section('content')

    @include('site.includes.main-banner')

    @if($landing_page->about_description['active'] == 1)

        @include('site.includes.about-us')

    @endif


    <!-- Start features -->
    @if($landing_page->our_features_description['active'] == 1)

        <section class="features" id="features-sec">
            <div class="container">
                <div class="features-parent">
                    <img class="mobile-hand-img" data--aos-duration="1000" data-aos-delay="1500" data-aos="fade-right"
                         data-aos-easing="ease-out-cubic" data-aos-duration="700"
                         src="{{ url('/storage/' . $landing_page->our_features_image)}}" alt="">
                    <div class="features-cont">
                        <div class="titles-parent">
                            <h4 class="sub-title">{{trans('site.some_greate_feature')}}</h4></h4>
                            <h2 class="general-title">
                                {{$landing_page->our_features_description[app()->getLocale()]??''}}
                            </h2>
                        </div>
                        <div class="features-list">
                            @foreach($landing_page->features as $feature)
                                <div class="list-item">
                                    <div class="icon-circle">
                                        <i class="fa-regular fa-{{$feature['icon']??''}} icon"></i>
                                    </div>
                                    <h4 class="item-title"> {{$feature['title'][app()->getLocale()]??''}} </h4>
                                </div>

                            @endforeach

                        </div>
                    </div>
                </div>
            </div>


        </section>

    @endif
    <!-- End features -->



    <!-- Start app-screens -->
    @if($landing_page->app_screen['active'] == 1)
        <section class="app-screens" id="appScreens-sec">
            <h3 class="title-text" data-aos="zoom-in-down" data-aos-easing="ease-out-cubic" data-aos-duration="1000">
                {{trans('site.images_from_app')}}</h3>
            <div class="mySwiper-parent">
                <div class="swiper">
                    <div class="swiper-wrapper">
                        @foreach($landing_page->app_screen['image']??[] as $image)
                            <div class="swiper-slide">

                                <div class="screenShot">
                                    <img src="{{ url('/storage/' . $image['image'])}}" alt="">
                                </div>
                            </div>

                        @endforeach


                    </div>
                </div>
                <div class="swiper-pagination"></div>
                <div class="frame">
                    <img src="images/frame.png" alt="">
                </div>

            </div>

        </section>
    @endif
    @include("site.includes.clients-reviews")
    @include("site.includes.faqs")

    @include('site.includes.apps')

    @include('site.includes.contacts')


 <!-- to top button  -->
  <button class="toTop">
    <i class="fa-light toTop-icon fa-arrow-up"></i>
  </button>
  <!-- end of to top button -->





  @endsection
