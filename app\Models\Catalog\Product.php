<?php

namespace Tasawk\Models\Catalog;

use ChristianKuri\LaravelFavorite\Traits\Favoriteable;

use Cknow\Money\Casts\MoneyDecimalCast;
use Cknow\Money\Money;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Notification;
use Tasawk\Models\Branch;
use Tasawk\Models\Catalog\Branch\Inventory;
use Tasawk\Models\Catalog\Product\ProductOption;
use Tasawk\Models\Customer;
use Tasawk\Models\Order;
use Tasawk\Models\Order\ItemsLine;
use Tasawk\Notifications\Product\NewProductCreatedNotification;
use Tasawk\Settings\GeneralSettings;
use Tasawk\Traits\Publishable;
use BezhanSalleh\FilamentShield\Traits\HasPanelShield;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;
use Spatie\Translatable\HasTranslations;
use Tasawk\Traits\Searchable;

/**
 * @method  whereBelongToPresentedBranchHeader()
 * */
class Product extends Model implements HasMedia {
    use  HasTranslations, InteractsWithMedia, Publishable;
    use Favoriteable, Searchable;

    protected $fillable = ['title', 'description', 'price', 'sale_price', 'status', 'more_data'];
    public array $translatable = ['title', 'description'];
    protected $appends = ['image'];
    protected $casts = [
        'more_data' => 'array',
//        'price' => MoneyDecimalCast::class,
//        'sale_price' => MoneyDecimalCast::class,
    ];

    protected static function booted() {
        parent::booted();
        static::created(function (Product $product) {
            dispatch(fn()=> Notification::send(Customer::get(), new NewProductCreatedNotification($product)))->afterResponse();
        });
    }

    public function scopeWhereBelongToPresentedBranchHeader($builder) {
        if (request()->header('X-Branch-ID')) {
            return $builder->whereHas('inventories', fn($builder) => $builder->where('branch_id', request()->header('X-Branch-ID')));
        }
        return $builder;
    }

    public function scopeFiltered($builder) {
        return $builder->when(request()->input('category_id'), function ($query) {
            return $query->whereHas('categories', function ($query) {
                $query->where('id', request()->input('category_id'));
            });
        });
    }


    public function getFullPriceAttribute() {
        return $this->hasOffer() ? $this->sale_price : $this->price;
    }

    public function getFullPriceIncludeTaxesAttribute() {
        return $this->hasOffer() ? $this->sale_price_include_taxes : $this->price_include_taxes;
    }

    public function getImageAttribute() {
        return $this->getFirstMediaUrl();
    }

    public function hasOffer(): bool {

        $sale_price = abs($this->sale_price->getAmount());
        return $sale_price > 0 && $sale_price < $this->price->getAmount();
    }

//    public function options() {
//        return $this->belongsToMany(Option::class);
//    }
    public function options(): HasMany {
        return $this->hasMany(ProductOption::class, 'product_id');
    }

    public function allergens(): BelongsToMany {
        return $this->belongsToMany(Allergen::class);
    }

    public function categories(): BelongsToMany {
        return $this->belongsToMany(Category::class);
    }

    public function branchInventory($id = null): Model|null {
        return $this->inventories()->where('branch_id', $id ?? request()->header('X-Branch-ID'))->first();
    }

    public function inventories(): HasMany {
        return $this->hasMany(Inventory::class);
    }

    public function recommendations(): BelongsToMany {
        return $this->belongsToMany(Product::class, 'product_recommendation', 'product_id', 'recommended_product_id');
    }

    public function orders() {
        return $this->hasManyThrough(Order::class, ItemsLine::class, "model->id", 'id', 'id', 'order_id');
    }

    public function price(): Attribute {
        return Attribute::make(
            get: fn($value) => Money::parse($value)
        );
    }

    public function salePrice(): Attribute {
        return Attribute::make(
            get: fn($value) => Money::parse($value)
        );
    }

    public function priceIncludeTaxes(): Attribute {
        return Attribute::make(
            get: fn($value) => Money::parse($this->price->add($this->priceTaxes()))
        );
    }

    public function priceTaxes() {
        $taxes = (new GeneralSettings())->taxes / 100;
        $taxes = $this->price->formatByDecimal()*$taxes;
        return Money::parse(bcdiv ( $taxes,1,2));
    }

    public function salePriceTaxes() {

        $taxes = (new GeneralSettings())->taxes / 100;
        return $this->sale_price->multiply($taxes);
    }

    public function salePriceIncludeTaxes(): Attribute {
        return Attribute::make(
            get: fn($value) => Money::parse($this->sale_price->add($this->salePriceTaxes()))
        );
    }

    public function isAvailableInAllBranches(): bool {
        return $this->inventories()->whereHas('branch',fn($q)=>$q->where('status',1))->count() == Branch::count();
    }

}
