@if(($faqs)->count() > 0 && $landing_page->faq['active'])
<section class="faq" id="faq-sec">
    <div class="container">
        <div class="titles-parent">
            <h4 class="sub-title animate__fadeInDown animate__animated wow"> {{ trans('site.faqs') }}</h4>
            <h2 class="general-title wow animate__pulse animate__animated">
                {{ trans('site.faqs_desc') }}
            </h2>
        </div>
        <div class="questions-parent">
            @foreach ($faqs as $faq)
                <div class="main-question ">
                    <div class="ques-details">
                        <div class="ques-num">
                            {{ $loop->iteration }}
                        </div>
                        <h3 class="ques-text"> {{ $faq->question }}
                        </h3>
                        <i class="fa-regular icon fa-plus"></i>
                    </div>
                    <div class="ques-answer">
                        {{ $faq->answer }}
                    </div>
                </div>
            @endforeach
        </div>
    </div>
</section>
@endif
