@php
    $tree = \Tasawk\Http\Resources\CategoriesTreeResource::collection(\Tasawk\Models\Catalog\Category::whereNull("parent_id")->get());
@endphp
<div class="bg-white p-4 rounded"
     x-load-css="@js(\Filament\Support\Facades\FilamentAsset::register([
    \Filament\Support\Assets\Css::make('custom-stylesheet', asset('css/custom/jstree/jstree.min.css')),
    \Filament\Support\Assets\Css::make('custom-stylesheet', asset('css/custom/jstree/ext-component-tree.min.css')),
    \Filament\Support\Assets\Css::make('fontawesome', asset('https://pro.fontawesome.com/releases/v5.10.0/css/all.css')),
]))"
     x-load-js="@js(\Filament\Support\Facades\FilamentAsset::register([
       \Filament\Support\Assets\Js::make('jquery', 'https://cdnjs.cloudflare.com/ajax/libs/jquery/3.7.1/jquery.min.js'),
       \Filament\Support\Assets\Js::make('jstree', 'https://cdnjs.cloudflare.com/ajax/libs/jstree/3.3.15/jstree.min.js'),

]))"
>
    <div id="categories_tree"></div>

</div>


<script>
    window.addEventListener("DOMContentLoaded", function () {
        let categoryTreeSelector = $("#categories_tree");

        initTree(categoryTreeSelector);

        function sendCategoriesArrangementList(list) {
            $.ajax({
                url: "{{route("cp.categories.arrange")}}",
                data: {list},
                success: function (data) {
                    new FilamentNotification()
                        .title('Saved successfully')
                        .icon('heroicon-o-document-text')
                        .iconColor('success')
                        .send()
                }
            })
        }

        function initTree(selector) {
            if (selector.length) {
                selector.jstree({
                    core: {
                        check_callback: true,
                        data: @json($tree)
                    },
                    plugins: ['types', 'dnd'],
                    types: {
                        default: {
                            icon: "fas fa-folder-open"
                        },
                    }
                }).on('move_node.jstree', function (e, data) {
                    let list = [], id, parent;
                    let treeList = categoryTreeSelector.jstree(true).get_json('#', {flat: true})
                    treeList.forEach(function (el) {
                        parent = (el.parent === "#") ? null : el.parent;
                        id = el.id;
                        list.push({"id": id, "parent": parent});
                    });
                    sendCategoriesArrangementList(list);

                });
            }
        }
    });
</script>

