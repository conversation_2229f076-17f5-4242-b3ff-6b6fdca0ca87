<?php

namespace Tasawk\Http\Resources\Api\Customer\Products;

use Illuminate\Http\Resources\Json\JsonResource;
use Tasawk\Lib\Utils;

class ProductResource extends JsonResource {


    public function toArray($request) {
        return [
            'id' => $this->id,
            'title' => $this->title,
            'description' => $this->description,
            'image' => $this->getFirstMediaUrl(),
            'origin_price' => $this->price_include_taxes->format(),
            'sale_price' => $this->sale_price_include_taxes->format(),
            'final_price' => $this->full_price_include_taxes->format(),
            'final_price_without_format' => $this->full_price_include_taxes->formatByDecimal(),
            'has_offer' => $this->hasOffer(),
            'available' => $this->isActive() ? ($this->branchInventory()?->isAvailable() ?? true) : false,
            'available_in_all_branches' => $this->isAvailableInAllBranches(),

            'status' => $this->isActive() ,

            'calories' => [
                'from' => $this->more_data['calories_from']??0,
                'to' => $this->more_data['calories_to']??0
                ],
            'allergens' => AllergenResource::collection($this->allergens),

            $this->mergeWhen(Utils::getBranchFromRequestHeader() && $this->branchInventory()?->exists(), [
                'period' => [
                    'from' => $this->branchInventory()['from'] ?? '',
                    'to' => $this->branchInventory()['to'] ?? ''
                ],
                'works_for_while' => $this?->branchInventory()['from'] ??''&& $this?->branchInventory()['to']??'',
                'options' => ProductOptionResource::collection($this->branchInventory()?->enabledOptions()?->whereOriginOptionEnabled()?->get() ?? []),
                'available' => $this->branchInventory()?->isAvailable(),
                'favorite' => (boolean)$request->user('sanctum')?->isFavorited($this),

            ]),


        ];
    }
}
