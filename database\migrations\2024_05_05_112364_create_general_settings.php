<?php

use Spatie\LaravelSettings\Migrations\SettingsMigration;

return new class extends SettingsMigration
{
    public function up(): void
    {
        $this->migrator->add('landing.logo', '');
     
        $this->migrator->add('landing.about_description', [
            'active' => 0,
            'ar' => 'شركة كوفه للأغذية من أفضل مقدمي منتج البروستد (الدجاج المقلي) بالمملكة العربية السعودية، لدينا أكثر من 20 فرع، طورنا تطبيق جوال يسهل على العملاء طلب وجباتهم المفضلة سواء للتوصيل للمنزل او الاستلام من الفرع دون عناء الانتظار بالإضافة إلى العديد من المزايا الرائعة
            ',
            'en' => 'Kufa Foods Company is one of the best providers of broasted (fried chicken) product in the Kingdom of Saudi Arabia. We have more than 20 branches. We have developed a mobile application that makes it easy for customers to order their favorite meals, whether for home delivery or pick-up from the branch, without the hassle of waiting, in addition to many wonderful advantages.',
        ]);
        $this->migrator->add('landing.about_features', [
            [
                'title' => [
                    'ar' => 'اطلب وجبتك اونلاين في دقائق',
                    'en' => 'Order your meal online in minutes',
                ],
                'icon' => ''
            ],
            [
                'title' => [
                    'ar' => 'خيارات دفع اونلاين متعددة وموثوقة',
                    'en' => 'Online payment options Versatile and reliable'
                ],
                'icon' => ''
            ],
            [
                'title' => [
                    'ar' => 'تابع تفاصيل وحالة طلبك حتى الإستلام',
                    'en' => 'Follow the order details and status until receipt'
                ],
                'icon' => "stopwatch"
            ],
            [
                'title' => [
                    'ar' => 'تقييم للطلبات لتحسين جودة الخدمة بإستمرار',
                    'en' => 'Evaluation of orders to continually improve service quality'
                ],
                'icon' => "user-headset"
            ]
        ]);
        $this->migrator->add('landing.our_features_description', [
            'active' => 0,
            'ar' => 'لأنك عميل مميز .. طورنا نظام يسهل عليك طلب وجبتك من مطاعم كوفه',
            'en' => "As a valued customer, we've streamlined meal ordering from Kufa restaurants just for you.",
        ]);

        $this->migrator->add('landing.our_features_image', '');

        $this->migrator->add('landing.features', [
            [
                'title' => [
                    'ar' => 'اختر الفرع الأقرب إليك من قائمة فروعنا',
                    'en' => 'Choose the closest branch to you from our list of branches'
                ],
                'icon' => 'delivery-fast'
            ],
            [
                'title' => [
                    'ar' => 'تحديد نوع واضافات الوجبة',
                    'en' => 'Determine the type and additions of the meal'
                ],
                'icon' => 'delivery-fast'
            ],
            [
                'title' => [
                    'ar' => 'تعرف على السعرات الحرارية ومسببات الحساسية ',
                    'en' => 'Learn about calories and allergens'
                ],
                'icon' => 'delivery-fast'
            ],
            [
                'title' => [
                    'ar' => 'خيارات متعددة وموثوقة للدفع',
                    'en' => 'Multiple and reliable payment options'
                ],
                'icon' => 'delivery-fast'
            ],
            [
                'title' => [
                    'ar' => 'تابع تفاصيل وحالة الطلب حتى الإستلام',
                    'en' => 'Follow up the order status in detail until receipt'
                ],
                'icon' => 'delivery-fast'
            ],
            [
                'title' => [
                    'ar' => 'فاتورة إلكترونية لكل عملية دفع',
                    'en' => 'An electronic invoice for each payment'
                ],
                'icon' => 'delivery-fast'
            ]
        ]);

        $this->migrator->add('landing.app_screen', [
            'active' => 0,
            'image' => '',
        ]);

        $this->migrator->add('landing.clients_reviews', [
            'active' => 0,
        ]);

        $this->migrator->add('landing.faq', [
            'active' => 0,
        ]);

        $this->migrator->add('landing.contact_us', [
            'active' => 0,
        ]);

       
    }
};
