<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Tasawk\Models\Zone;
use Tasawk\Models\Branch;
use Tasawk\Models\Manager;
use Tasawk\Models\Operator;
use MatanYadaev\EloquentSpatial\Objects\Point;
use MatanYadaev\EloquentSpatial\Objects\Polygon;
use MatanYadaev\EloquentSpatial\Objects\LineString;

class AdditionalBranchesSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Create additional managers if needed
        $existingManagers = Manager::count();
        if ($existingManagers < 5) {
            $additionalManagers = [
                [
                    'name' => 'Sara Al-Khalifa',
                    'email' => '<EMAIL>',
                    'phone' => '+96512345004',
                    'password' => Hash::make('password123'),
                    'active' => true,
                ],
                [
                    'name' => 'Youssef Al-Sabah',
                    'email' => '<EMAIL>',
                    'phone' => '+96512345005',
                    'password' => Hash::make('password123'),
                    'active' => true,
                ],
            ];

            foreach ($additionalManagers as $managerData) {
                Manager::create($managerData);
            }
        }

        // Create additional operators if needed
        $existingOperators = Operator::count();
        if ($existingOperators < 8) {
            $additionalOperators = [
                [
                    'name' => 'Maryam Al-Rashid',
                    'email' => '<EMAIL>',
                    'phone' => '+96512345106',
                    'password' => Hash::make('password123'),
                    'active' => true,
                ],
                [
                    'name' => 'Hassan Al-Mutairi',
                    'email' => '<EMAIL>',
                    'phone' => '+96512345107',
                    'password' => Hash::make('password123'),
                    'active' => true,
                ],
                [
                    'name' => 'Aisha Al-Ahmad',
                    'email' => '<EMAIL>',
                    'phone' => '+96512345108',
                    'password' => Hash::make('password123'),
                    'active' => true,
                ],
            ];

            foreach ($additionalOperators as $operatorData) {
                Operator::create($operatorData);
            }
        }

        // Create additional branches
        $existingBranches = Branch::count();
        if ($existingBranches < 5) {
            $zones = Zone::all();
            
            $additionalBranches = [
                [
                    'name' => ['en' => 'Kufa East Branch', 'ar' => 'فرع الكوفة الشرقي'],
                    'phone' => '+96512340004',
                    'email' => '<EMAIL>',
                    'address_name' => 'Hawalli District, East Kuwait',
                    'zone_id' => $zones->count() >= 4 ? $zones[3]->id : $zones->first()->id,
                    'location' => ['lat' => 29.3326, 'lng' => 48.0281], // Hawalli coordinates
                    'boundaries' => new Polygon([
                        new LineString([
                            new Point(48.0200, 29.3250),
                            new Point(48.0350, 29.3250),
                            new Point(48.0350, 29.3400),
                            new Point(48.0200, 29.3400),
                            new Point(48.0200, 29.3250),
                        ])
                    ]),
                    'receipt_methods' => ['delivery', 'pickup'],
                    'maintenance_mode' => false,
                    'heavy_load_mode' => false,
                    'working_days' => [
                        'sunday' => ['from' => '08:00', 'to' => '22:00', 'active' => true],
                        'monday' => ['from' => '08:00', 'to' => '22:00', 'active' => true],
                        'tuesday' => ['from' => '08:00', 'to' => '22:00', 'active' => true],
                        'wednesday' => ['from' => '08:00', 'to' => '22:00', 'active' => true],
                        'thursday' => ['from' => '08:00', 'to' => '22:00', 'active' => true],
                        'friday' => ['from' => '14:00', 'to' => '22:00', 'active' => true],
                        'saturday' => ['from' => '08:00', 'to' => '22:00', 'active' => true],
                    ],
                ],
                [
                    'name' => ['en' => 'Kufa West Branch', 'ar' => 'فرع الكوفة الغربي'],
                    'phone' => '+96512340005',
                    'email' => '<EMAIL>',
                    'address_name' => 'Farwaniya District, West Kuwait',
                    'zone_id' => $zones->count() >= 5 ? $zones[4]->id : $zones->first()->id,
                    'location' => ['lat' => 29.2775, 'lng' => 47.9589], // Farwaniya coordinates
                    'boundaries' => new Polygon([
                        new LineString([
                            new Point(47.9500, 29.2700),
                            new Point(47.9650, 29.2700),
                            new Point(47.9650, 29.2850),
                            new Point(47.9500, 29.2850),
                            new Point(47.9500, 29.2700),
                        ])
                    ]),
                    'receipt_methods' => ['delivery', 'pickup'],
                    'maintenance_mode' => false,
                    'heavy_load_mode' => false,
                    'working_days' => [
                        'sunday' => ['from' => '09:00', 'to' => '21:00', 'active' => true],
                        'monday' => ['from' => '09:00', 'to' => '21:00', 'active' => true],
                        'tuesday' => ['from' => '09:00', 'to' => '21:00', 'active' => true],
                        'wednesday' => ['from' => '09:00', 'to' => '21:00', 'active' => true],
                        'thursday' => ['from' => '09:00', 'to' => '21:00', 'active' => true],
                        'friday' => ['from' => '15:00', 'to' => '21:00', 'active' => true],
                        'saturday' => ['from' => '09:00', 'to' => '21:00', 'active' => true],
                    ],
                ],
            ];

            foreach ($additionalBranches as $branchData) {
                Branch::create($branchData);
            }
        }

        // Now assign managers and operators to all branches
        $allBranches = Branch::all();
        $allManagers = Manager::all();
        $allOperators = Operator::all();

        // Assign managers to branches that don't have them
        foreach ($allBranches as $index => $branch) {
            $hasManager = DB::table('branch_manager')->where('branch_id', $branch->id)->exists();
            if (!$hasManager && isset($allManagers[$index])) {
                DB::table('branch_manager')->insert([
                    'branch_id' => $branch->id,
                    'manager_id' => $allManagers[$index]->id,
                ]);
                $this->command->info("Assigned manager {$allManagers[$index]->name} to branch {$branch->name}");
            }
        }

        // Assign operators to branches that need them
        $operatorIndex = 0;
        foreach ($allBranches as $branch) {
            $currentOperators = DB::table('branch_account')->where('branch_id', $branch->id)->count();
            $neededOperators = 2 - $currentOperators; // Each branch should have 2 operators
            
            for ($i = 0; $i < $neededOperators && $operatorIndex < $allOperators->count(); $i++) {
                DB::table('branch_account')->insert([
                    'branch_id' => $branch->id,
                    'account_id' => $allOperators[$operatorIndex]->id,
                ]);
                $this->command->info("Assigned operator {$allOperators[$operatorIndex]->name} to branch {$branch->name}");
                $operatorIndex++;
            }
        }

        $this->command->info('Additional branches and relationships created successfully!');
        $this->command->info('Total Zones: ' . Zone::count());
        $this->command->info('Total Branches: ' . Branch::count());
        $this->command->info('Total Managers: ' . Manager::count());
        $this->command->info('Total Operators: ' . Operator::count());
        $this->command->info('Branch-Manager relations: ' . DB::table('branch_manager')->count());
        $this->command->info('Branch-Account relations: ' . DB::table('branch_account')->count());
    }
}
