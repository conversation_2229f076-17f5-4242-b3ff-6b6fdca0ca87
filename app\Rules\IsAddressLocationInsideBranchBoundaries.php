<?php

namespace Tasawk\Rules;

use Closure;
use Illuminate\Contracts\Validation\ValidationRule;
use MatanYadaev\EloquentSpatial\Objects\Point;
use Tasawk\Lib\Utils;
use Tasawk\Models\AddressBook;
use Tasawk\Models\Branch;

class IsAddressLocationInsideBranchBoundaries implements ValidationRule {
    /**
     * Run the validation rule.
     *
     * @param \Closure(string): \Illuminate\Translation\PotentiallyTranslatedString $fail
     */
    public function validate(string $attribute, mixed $value, Closure $fail): void {
        $address = AddressBook::find(request()->get('address_id'));

        if (
            $address &&
            !Branch::where('id', Utils::getBranchFromRequestHeader())
                ->whereContains('boundaries', new Point($address?->map_location['lat']??'', $address?->map_location['lng']??''))
                ->exists()
        ) {
            $fail(__("validation.api.address_out_side_current_branch"));
        }
    }
}
