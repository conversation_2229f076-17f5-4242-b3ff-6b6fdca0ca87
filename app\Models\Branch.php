<?php

namespace Tasawk\Models;


use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Matan<PERSON><PERSON>ev\EloquentSpatial\Objects\Geometry;
use MatanYadaev\EloquentSpatial\Objects\Point;
use Matan<PERSON><PERSON>ev\EloquentSpatial\Objects\Polygon;
use MatanYadaev\EloquentSpatial\Traits\HasSpatial;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;
use Spatie\Translatable\HasTranslations;
use Str;
use Tasawk\Lib\Utils;
use Tasawk\Models\Catalog\Branch\BranchAccount;
use Tasawk\Models\Catalog\Branch\Inventory;
use Tasawk\Models\Catalog\Option;
use Tasawk\Models\Catalog\Product;
use Tasawk\Models\Catalog\Value;
use Tasawk\Notifications\Branch\BranchMaintenanceModeChangedNotification;

class Branch extends Model implements HasMedia {
    use HasFactory, HasTranslations, InteractsWithMedia;
    use HasSpatial;


    protected $fillable = [
        'name',
        'location',
        'phone',
        'email',
        'address_name',
        'zone_id',
        'latitude',
        'longitude',
        'boundaries',
        'receipt_methods',
        'maintenance_mode',
        'heavy_load_mode',
        'working_days'
    ];
    /**
     * The following code was generated for use with Filament Google Maps
     *
     * php artisan fgm:model-code Branch --lat=latitude --lng=longitude --location=location --terse
     */


    public array $translatable = ['name'];


    protected $appends = [
        'location',
    ];
    protected $casts = [
        'receipt_methods' => 'array',
        'working_days' => 'array',
        'boundaries' => Polygon::class,
        'location' => Point::class
    ];

    protected static function booted() {
        parent::booted(); // TODO: Change the autogenerated stub
        static::updating(function ($branch) {

            if ($branch->getOriginal('maintenance_mode') != $branch->maintenance_mode) {
                \Notification::send(Utils::getAdministrationUsers(), new BranchMaintenanceModeChangedNotification($branch));
            }
        });

        static::addGlobalScope('users', function ($builder) {
            $builder->when(auth()->user()?->hasRole('manager'), fn($q) => $q->whereHas('managers', fn($q1) => $q1->where('manager_id', auth()->user()->id)))
                ->when(auth()->user()?->hasRole('operator'), fn($q) => $q->whereHas('operators', fn($q1) => $q1->where('account_id', auth()->user()->id)));
        });
    }

    public function scopeCanAcceptOrders($builder) {
        return $builder->where('maintenance_mode', 0);
    }

    public function zone() {
        return $this->belongsTo(Zone::class);
    }

//    public function manager(): BelongsTo {
//        return $this->belongsTo(User::class, 'manager_id');
//    }

    public function products(): BelongsToMany {
        return $this->belongsToMany(Product::class)->withPivot("status", 'from', 'to');
    }

    public function options(): \Illuminate\Database\Eloquent\Relations\belongsToMany {
        return $this->belongsToMany(Option::class, 'branch_product_option')->withPivot('required');
    }

    public function managers(): \Illuminate\Database\Eloquent\Relations\belongsToMany {
        return $this->belongsToMany(Manager::class, 'branch_manager', 'branch_id', 'manager_id', `branches.id`, 'id');
    }

    public function operators(): BelongsToMany {
        return $this->belongsToMany(Operator::class, BranchAccount::class, 'branch_id', 'account_id');
    }

    public function values(): \Illuminate\Database\Eloquent\Relations\belongsToMany {
        return $this->belongsToMany(Value::class, 'branch_product_option_value');
    }

    public function inventories(): HasMany {
        return $this->hasMany(Inventory::class);
    }

    protected function boundaries(): Attribute {
        return Attribute::make(
            set: function ($value) {
                $this->refresh();

                return Geometry::fromJson($value)->toSqlExpression($this->getConnection());
            }
        );
    }

    protected function location(): Attribute {
        return Attribute::make(
            set: function ($coordinate) {
                return (new Point($coordinate['lat'], $coordinate['lng']))->toSqlExpression($this->getConnection());
            }
        );
    }

    public function availableNow(): bool {

        $day_name = Str::lower(now()->locale("en")->dayName);
        $workingDays = collect($this->working_days)->map(fn($slot) => array_values($slot))->collapse();
        if (!$workingDays->count()) {
            return false;
        }

        foreach ($workingDays->where('day_name', $day_name)->where('status', 1) as $slot) {
            if (now()->isBetween($slot['from'], $slot['to'])) {
                return true;
            };
        }
        return false;
    }

    public function todayPeriods(): array {
        $periods = [];
        $day_name = Str::lower(now()->locale("en")->dayName);
        $workingDays = collect($this->working_days)->map(fn($slot) => array_values($slot))->collapse();

        if (!$workingDays->count()) {
            return [];
        }

        foreach ($workingDays->where('day_name', $day_name)->where('status', 1) as $slot) {
//            if (now()->isBetween($slot['from'], $slot['to'])) {
                $periods [] = [
                    'from' => $slot['from'],
                    'to' => $slot['to'],
                ];
//            };
        }
        return $periods;
    }

    public function orders() {
        return $this->hasMany(Order::class);
    }


}
