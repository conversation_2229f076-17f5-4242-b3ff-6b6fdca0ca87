<?php

namespace Tasawk\Filament\Resources\Employees;

use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Actions\Action;
use Filament\Tables\Columns\IconColumn;
use Filament\Tables\Columns\SpatieMediaLibraryImageColumn;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Filters\Filter;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Tasawk\Enum\ModelStatus;
use Tasawk\Filament\Resources\Employees\ManagerResource\Pages;
use Tasawk\Filament\Shared\Schema\UserSchema;
use Tasawk\Models\Branch;
use Tasawk\Models\Manager;
use Tasawk\Traits\Filament\HasTranslationLabel;

class ManagerResource extends Resource {
    use HasTranslationLabel;

    protected static ?string $model = Manager::class;
    protected static ?string $navigationIcon = 'heroicon-o-users';
    protected static ?int $navigationSort = 1;

    public static function form(Form $form): Form {
        $branches = Branch::whereHas('managers')->pluck('id')->toArray();
        return $form->schema(
            array_merge(UserSchema::fetch(), [
                Select::make("branch")
                    ->relationship("branch", "name")
                    ->getOptionLabelFromRecordUsing(fn(Branch $record) => "{$record->getTranslation('name','en')} - {$record->getTranslation('name','ar')}")
                    ->searchable(['name->ar', 'name->en'])
                    ->disableOptionWhen(fn(string $value, $record): bool => in_array($value, $branches))
                    ->columnSpan(2)
                    ->preload()

            ])

        );

    }

    public static function table(Table $table): Table {
        return $table
            ->columns([
                TextColumn::make('id')->searchable(),
                SpatieMediaLibraryImageColumn::make('avatar'),
                TextColumn::make('name')->searchable(),
                TextColumn::make('email')
                    ->copyable()
                    ->copyMessage('Email address copied')
                    ->copyMessageDuration(1500)
                    ->searchable(),
                TextColumn::make('branch.name')
                    ->label(__('forms.fields.branch'))
                    ->default("N/A")
                    ->searchable(),
                TextColumn::make('phone')
                    ->searchable()
                    ->copyable()
                    ->copyMessage('Phone address copied')
                    ->copyMessageDuration(1500),
                IconColumn::make('active')
                    ->boolean()
                    ->action(
                        Action::make('Active')
                            ->label(fn(Model $record): string => $record->active ? __('panel.messages.deactivate') : __('panel.messages.activate'))
                            ->disabled(fn(Model $record): bool => !auth()->user()->can('update', $record))
                            ->requiresConfirmation()
                            ->action(fn(Model $record) => $record->toggleActive())


                    )
            ])
            ->filters([
                Filter::make('ID')
                    ->form([
                        TextInput::make('id'),
                    ])
                    ->query(function (Builder $query, array $data): Builder {
                        return $query->when($data['id'], fn(Builder $query, $date): Builder => $query->where('id', $data['id']));
                    })
                    ->indicateUsing(function (array $data): ?string {
                        if (!$data['id']) {
                            return null;
                        }

                        return __('forms.fields.id') . " " . $data['id'];
                    }),
                SelectFilter::make('active')
                    ->options(ModelStatus::class)
            ])
            ->actions([


                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),

            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])
            ->emptyStateActions([
                Tables\Actions\CreateAction::make(),
            ])
            ->striped();

    }

    public static function getRelations(): array {
        return [
            //
        ];
    }

    public static function getPages(): array {
        return [
            'index' => Pages\ListManagers::route('/'),
//            'create' => Pages\CreateManager::route('/create'),
//            'edit' => Pages\EditManager::route('/{record}/edit'),
        ];
    }

    public static function getNavigationGroup(): ?string {
        return __('menu.employees');
    }

    public static function getNavigationBadge(): ?string {
        return static::getModel()::count();
    }
}
