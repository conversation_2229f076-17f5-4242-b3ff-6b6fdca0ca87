<?php

namespace Tasawk\Filament\Widgets;

use <PERSON><PERSON><PERSON><PERSON><PERSON>h\FilamentShield\Traits\HasWidgetShield;
use Filament\Tables;
use Filament\Tables\Columns\SpatieMediaLibraryImageColumn;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;
use Filament\Widgets\TableWidget as BaseWidget;
use Tasawk\Models\Catalog\Product;

class BestSellingProducts extends BaseWidget {
    use HasWidgetShield;
    protected static ?int $sort = 7;
    public function table(Table $table): Table {
        return $table
            ->heading(__('sections.best_selling_products'))
            ->description(__('sections.best_selling_products_description'))
            ->query(
                Product::whereHas('orders')->withCount('orders')->orderBy("orders_count", 'desc')->limit(5),
            )
            ->columns([
                TextColumn::make('index')->rowIndex(),
                SpatieMediaLibraryImageColumn::make('image'),

                TextColumn::make('title'),
                TextColumn::make('orders_count'),
            ]);
    }

     public function getTableHeading(): ?string {
        return __('sections.best_selling_products');
    }
}
