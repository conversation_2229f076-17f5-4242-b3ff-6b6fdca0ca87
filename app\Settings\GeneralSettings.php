<?php

namespace Tasawk\Settings;

use <PERSON><PERSON>\LaravelSettings\Settings;

class GeneralSettings extends Settings {
    public string|null $app_logo;
    public string $app_name;
    public string $app_email;
    public string $app_phone;
    public string $app_address;
    public float $taxes;
    public bool $enable_delivery_mode;
    public bool $enable_orders_discount_upon_receipt_from_the_branch;
    public int $delivery_cost;
    public int $diameter;
    public int $min_time_to_deliver_order;
    public int $max_time_to_deliver_order;
    public int $orders_discount_upon_receipt_from_the_branch;
    public int $delivery_cost_for_each_additional_kilometer;
    public int $order_limit_per_item;
    public array $applications_links = [];
    public array $app_pages = [];

    public array $social_links = [];
    public array $working_days = [];
    public static function group(): string {
        return 'general';
    }
}
