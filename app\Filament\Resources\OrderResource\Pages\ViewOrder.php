<?php

namespace Tasawk\Filament\Resources\OrderResource\Pages;

use Filament\Actions\Action;
use Filament\Forms\Components\Select;
use Filament\Resources\Pages\ViewRecord;
use Str;
use Tasawk\Api\Facade\Api;
use Tasawk\Enum\OrderPaymentStatus;
use Tasawk\Enum\OrderStatus;
use Tasawk\Filament\Resources\OrderResource;
use Tasawk\Filament\Resources\OrderResource\Widgets\CustomerOrderAddress;
use Tasawk\Models\CancellationReason;
use Tasawk\Models\Order;
use Tasawk\Notifications\Order\OrderCanceledNotification;

class ViewOrder extends ViewRecord {
    protected static string $resource = OrderResource::class;

    protected function getHeaderActions(): array {
        return [
            Action::make('changeStatus')
                ->label(__('panel.actions.change_status'))
                ->icon('heroicon-o-bolt')
                ->disabled(fn(Order $record) => !$record->getAvailableStatus()->count())
                ->form([
                    Select::make('status')
                        ->options(fn($record) => $record->getAvailableStatus()->pluck('label', 'value')->toArray())
                        ->required(),
                ])
                ->visible(fn(Order $record) => auth()->user()->can('update', $record))
                ->action(function (array $data, Order $record): void {
                    if (Str::of($data['status'])->contains("canceled-")) {
                        $record->cancel(Str::of($data['status'])->after("-"));
                        return;
                    }
                    if ($data['status'] == OrderStatus::DELIVERED->value && $record->payment_data['gateway'] =='cash') {
                        $record->update([
                            'payment_data' => array_merge($record->payment_data, ['method' => 'cash', 'paid_at' => now()]),
                            'payment_status' => 'paid',
                        ]);
                    }
                    $record->update(['status' => $data['status']]);
                }),
        ];
    }
    protected function getHeaderWidgets(): array {
     return [];
    }
}
