<?php

namespace Tasawk\Models\Content;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;
use Spatie\Translatable\HasTranslations;
use Tasawk\Traits\Publishable;

class CustomerReview extends Model implements HasMedia {
    use HasFactory, HasTranslations, Publishable, InteractsWithMedia;

    public array $translatable = ['customer_name', 'review'];
    protected $fillable = [
        'customer_name',
        'review',
        'rate',
        'status'
    ];

}
