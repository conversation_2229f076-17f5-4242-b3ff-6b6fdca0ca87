<?php

namespace Tasawk\Console\Commands;

use Illuminate\Console\Command;
use Tasawk\Models\Catalog\Product;
use Tasawk\Models\AddressBook;
use Tasawk\Models\Customer;
use Tasawk\Models\Branch;

class ShowCartData extends Command
{
    protected $signature = 'show:cart-data';
    protected $description = 'Display data needed for cart checkout request';

    public function handle()
    {
        $this->info('=== CART CHECKOUT REQUEST DATA ===');
        $this->newLine();

        // Get customer
        $customer = Customer::where('email', '<EMAIL>')->first();
        if (!$customer) {
            $this->error('No customer found <NAME_EMAIL>');
            return 1;
        }

        $this->info("Customer: {$customer->name} (ID: {$customer->id})");
        $this->info("Email: {$customer->email}");
        $this->newLine();

        // Get products
        $this->info('AVAILABLE PRODUCTS:');
        $products = Product::where('status', true)->take(5)->get();
        foreach ($products as $product) {
            $this->line("ID: {$product->id} - Title: {$product->title} - Price: {$product->price->format()}");
        }
        $this->newLine();

        // Get addresses
        $this->info('CUSTOMER ADDRESSES:');
        $addresses = AddressBook::where('user_id', $customer->id)->get();
        foreach ($addresses as $address) {
            $this->line("ID: {$address->id} - Name: {$address->name} - Address: {$address->street}, {$address->state}");
        }
        $this->newLine();

        // Get branches
        $this->info('AVAILABLE BRANCHES:');
        $branches = Branch::take(3)->get();
        foreach ($branches as $branch) {
            $this->line("ID: {$branch->id} - Name: {$branch->name} - Receipt Methods: " . implode(', ', $branch->receipt_methods ?? []));
        }
        $this->newLine();

        // Generate sample request
        $sampleProduct = $products->first();
        $sampleAddress = $addresses->first();
        $sampleBranch = $branches->first();

        if ($sampleProduct && $sampleAddress && $sampleBranch) {
            $this->info('SAMPLE CART CHECKOUT REQUEST BODY:');
            $this->newLine();
            
            $requestBody = [
                'receipt_method' => 'delivery', // or 'pickup', 'takeaway' based on branch receipt_methods
                'payment_gateway' => 'myfatoorah', // or 'cash'
                'address_id' => $sampleAddress->id,
                'notes' => 'Please deliver to the main entrance',
                'products' => [
                    [
                        'id' => $sampleProduct->id,
                        'quantity' => 2,
                        'options' => [] // Add product options if available
                    ]
                ]
            ];

            $this->line('```json');
            $this->line(json_encode($requestBody, JSON_PRETTY_PRINT));
            $this->line('```');
            $this->newLine();

            $this->info('HEADERS REQUIRED:');
            $this->line("X-Branch-ID: {$sampleBranch->id}");
            $this->line("Authorization: Bearer {customer_api_token}");
            $this->line("Content-Type: application/json");
            $this->line("Accept: application/json");
            $this->newLine();

            $this->info('ENDPOINT:');
            $this->line('POST /api/v1/customer/cart/checkout');
            $this->newLine();

            $this->info('NOTES:');
            $this->line('- receipt_method must be one of: ' . implode(', ', $sampleBranch->receipt_methods ?? ['delivery', 'pickup']));
            $this->line('- address_id is required only when receipt_method is "delivery"');
            $this->line('- payment_gateway can be "myfatoorah" or "cash"');
            $this->line('- X-Branch-ID header is required to specify which branch to order from');
            $this->line('- Customer must be authenticated (Bearer token required)');
        } else {
            $this->error('Missing required data for sample request generation');
        }

        return 0;
    }
}
