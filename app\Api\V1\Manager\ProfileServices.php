<?php

namespace Tasawk\Api\V1\Manager;

use Illuminate\Http\Request;
use Tasawk\Actions\Manager\UpdateManagerProfileAction;
use Tasawk\Actions\Shared\Authentication\UpdateUserPassword;
use Tasawk\Api\Core;
use Tasawk\Api\Facade\Api;
use Tasawk\Http\Requests\Api\Customer\Profile\ProfileSettingRequest;
use Tasawk\Http\Requests\Api\Manager\Profile\UpdateManagerPasswordRequest;
use Tasawk\Http\Requests\Api\Manager\Profile\UpdateManagerProfileRequest;
use Tasawk\Http\Resources\Api\Manager\ManagerResource;


class ProfileServices {
    public function index() {
        return Api::isOk(__("Manager information"))->setData(new ManagerResource(auth()->user()));
    }

    public function update(UpdateManagerProfileRequest $request) {
        UpdateManagerProfileAction::run($request);


        return Api::isOk(__("Account information updated"))->setData(new ManagerResource(auth()->user()));
    }

    public function updatePassword(UpdateManagerPasswordRequest $request): Core {
        UpdateUserPassword::run(auth()->user(), $request->get('password'));
        return Api::isOk(__("Account information updated"))->setData(new ManagerResource(auth()->user()));
    }
    public function settings(ProfileSettingRequest $request) {
        auth()->user()->update(['settings' => $request->validated()]);
        return Api::isOk(__("User settings updated"));
    }

}
