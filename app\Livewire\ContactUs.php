<?php

namespace Tasawk\Livewire;

use Livewire\Component;
use Tasawk\Models\Content\Contact;
use Tasawk\Models\Location\City;
use Tasawk\Models\Zone;
use Tasawk\Rules\KSAPhoneRule;

class ContactUs extends Component {
    public $name;
    public $email;
    public $phone;
    public $message;
    public $city_id;
    public $cities = [];
    public $successMessage;

    public function getRules() {
        return [
            'name' => ['required', 'min:3', 'max:255'],
            'email' => ['required', 'email', 'max:255'],
            'phone' => ['required', new KSAPhoneRule()],
            'message' => ['required', 'min:3', 'max:255'],
            'city_id' => ['required', 'exists:zones,id'],
        ];
    }

    public function getValidationAttributes() {
        return [
            'phone' => __('site.phone')
        ];
    }

    public function mount() {
        $this->cities = Zone::where('status', 1)->get();

    }

    public function updated($propertyName) {
        $this->validateOnly($propertyName);
    }

    public function submitForm() {
        $contact = $this->validate();
        $contact['title'] = $this->name;
        $contact['name'] = $this->name;
        $contact['email'] = $this->email;
        $contact['message'] = $this->message;
        $contact['phone'] = $this->phone;
        $contact['city_id'] = $this->city_id;
        Contact::create($contact);
        $this->reset();
        $this->successMessage = __('site.contact_us_success');

    }


    public function render() {
        return view('livewire.contact-us', [
            'cities' => $this->cities,
        ]);
    }
}
