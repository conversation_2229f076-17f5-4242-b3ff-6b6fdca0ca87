<?php

namespace Tasawk\Models\Catalog;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;
use Spatie\Translatable\HasTranslations;

class Value extends Model implements HasMedia {
    use HasFactory, HasTranslations,InteractsWithMedia;
    protected $fillable = ['name','value','sort'];
    protected static function booted() {
        parent::booted(); // TODO: Change the autogenerated stub
        static::creating(function ($data) {
            $data->value = $data->value;
        });
        static::updating(function ($data) {
            $data->value = $data->value;
        });
    }

    public array $translatable = ['value'];
    protected $table = 'option_values';

    public function option() {
        return $this->belongsTo(Option::class);
    }
}
