<?php

namespace Tasawk\Enum;

use Filament\Support\Contracts\HasLabel;

enum ReceiptMethods: string implements HasLabel {
    case TAKEAWAY = 'takeaway';
    case Local = 'local';
    case DELIVERY = 'delivery';

    public function getLabel(): ?string {
        return __("panel.enums.$this->value");

    }
    public function getColor(): string {
        return match ($this->value) {
            'takeaway' => 'warning',
            'local' => 'success',
            'delivery' => 'danger',
        };
    }
}
