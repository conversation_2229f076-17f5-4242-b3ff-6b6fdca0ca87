<?php

namespace Tasawk\Filament\Resources\Catalog\ProductResource\RelationManagers;

use Filament\Forms;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Toggle;
use Filament\Forms\Components\Wizard\Step;
use Filament\Forms\Form;
use Filament\Forms\Get;
use Filament\Notifications\Notification;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Actions\Action;
use Filament\Tables\Columns\IconColumn;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Collection;
use Str;
use Tasawk\Enum\OrderStatus;
use Tasawk\Models\Catalog\Branch\Inventory;
use Tasawk\Models\Catalog\Option;
use Tasawk\Models\Catalog\Product;
use Tasawk\Models\Catalog\Value;
use Tasawk\Models\Order;

class OptionRelationManager extends RelationManager {
    protected static string $relationship = 'options';
    protected bool $allowsDuplicates = false;

    public function form(Form $form): Form {
        return $form
            ->schema([])
            ->columns(1);
    }

    public function table(Table $table): Table {
        return $table
            ->heading(__('sections.options'))
            ->columns([
                Tables\Columns\TextColumn::make('id'),
                Tables\Columns\TextColumn::make('option.name'),
                IconColumn::make('status')
                    ->boolean()
                    ->action(
                        Action::make('Active')
                            ->label(fn(Product\ProductOption $record): string => $record->status ? __('panel.messages.deactivate') : __('panel.messages.activate'))
//                            ->disabled(fn(Model $record): bool => !auth()->user()->can('update', $record))
                            ->requiresConfirmation()
                            ->action(fn(Product\ProductOption $record) => $record->toggleStatus())

                    )
            ])
            ->filters([
                //
            ])
            ->headerActions([
                Tables\Actions\CreateAction::make()->form($this->wizardSchema()),

            ])
            ->actions([
                Tables\Actions\EditAction::make()->form($this->wizardSchema()),
                Tables\Actions\DeleteAction::make()
                    ->before(function (Tables\Actions\DeleteAction $action, Product\ProductOption $option) {
                        $option->inventoryOptions()->where("option_id",$option->id)->delete();
                    }),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public function wizardSchema(): array {
        return [

            Forms\Components\Select::make("option_id")
                ->label(__('forms.fields.option_name'))
                ->live()
                ->required()
                ->options(Option::get()->pluck('name', 'id'))
                ->searchable(),

            Toggle::make('required')
                ->default(1)
                ->onColor('success')
                ->offColor('danger'),
            Toggle::make('status')
                ->default(1)
                ->onColor('success')
                ->offColor('danger'),
            Forms\Components\Repeater::make("values")
                ->label(__('forms.fields.values'))
                ->relationship("values")
                ->schema([
                    Forms\Components\Select::make("value_id")
                        ->label(__("forms.fields.value_name"))
                        ->required()
                        ->live()
                        ->options(fn(Get $get): Collection => Value::query()
                            ->where('option_id', $get('../../option_id'))
                            ->get()
                            ->pluck('value', 'id'))
                        ->searchable(),
                    Forms\Components\TextInput::make("price")
                        ->required()
                        ->live()
                        ->formatStateUsing(fn($record) => $record?->price?->formatByDecimal() ?? 0)
                        ->suffix(__('forms.suffixes.sar'))
                        ->default(0),
                    Toggle::make('status')
                        ->onColor('success')
                        ->offColor('danger')
                        ->default(1),
                ])
                ->addActionLabel(__('forms.actions.add'))
                ->minItems(1)
                ->columns(3)

        ];
    }

    /**
     * @return string|null
     */
    public static function getModelLabel(): ?string {
     return __('sections.options');
    }
}
