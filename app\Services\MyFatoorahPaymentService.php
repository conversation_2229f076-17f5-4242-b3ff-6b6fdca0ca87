<?php

namespace Tasawk\Services;

use Exception;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;

class MyFatoorahPaymentService
{
    private string $apiKey;
    private string $baseUrl;
    private bool $isTest;
    private string $countryCode;

    public function __construct()
    {
        $this->apiKey = config('myfatoorah.api_key');
        $this->isTest = config('myfatoorah.test_mode', false);
        $this->countryCode = config('myfatoorah.country_iso', 'KWT');
        $this->baseUrl = $this->getBaseUrl();
    }

    /**
     * Get the correct base URL based on test mode and country
     */
    private function getBaseUrl(): string
    {
        if ($this->isTest) {
            return 'https://apitest.myfatoorah.com';
        }

        // Live URLs based on country
        return match ($this->countryCode) {
            'SAU' => 'https://api-sa.myfatoorah.com',
            'ARE' => 'https://api.myfatoorah.com',
            'QAT' => 'https://api-qa.myfatoorah.com',
            'BHR' => 'https://api.myfatoorah.com',
            'OMN' => 'https://api.myfatoorah.com',
            'JOD' => 'https://api.myfatoorah.com',
            'EGY' => 'https://api-eg.myfatoorah.com',
            'KWT' => 'https://api.myfatoorah.com',
            default => 'https://api.myfatoorah.com',
        };
    }

    /**
     * Get request headers
     */
    private function getHeaders(): array
    {
        return [
            'Authorization' => 'Bearer ' . $this->apiKey,
            'Content-Type' => 'application/json',
            'Accept' => 'application/json',
        ];
    }

    /**
     * Create invoice URL using SendPayment endpoint
     */
    public function createInvoice(array $paymentData): array
    {
        try {
            $url = $this->baseUrl . '/v2/SendPayment';
            
            $response = Http::withHeaders($this->getHeaders())
                ->timeout(30)
                ->post($url, $paymentData);

            if (!$response->successful()) {
                throw new Exception('MyFatoorah API request failed: ' . $response->body());
            }

            $responseData = $response->json();

            if (!$responseData['IsSuccess']) {
                $errorMessage = $responseData['Message'] ?? 'Unknown error occurred';
                if (isset($responseData['ValidationErrors']) && is_array($responseData['ValidationErrors'])) {
                    $errorMessage .= ': ' . implode(', ', $responseData['ValidationErrors']);
                }
                throw new Exception($errorMessage);
            }

            return $responseData['Data'];
        } catch (Exception $e) {
            Log::error('MyFatoorah CreateInvoice Error: ' . $e->getMessage(), [
                'payment_data' => $paymentData,
                'api_key_prefix' => substr($this->apiKey, 0, 10) . '...',
                'base_url' => $this->baseUrl
            ]);
            throw $e;
        }
    }

    /**
     * Get payment status using GetPaymentStatus endpoint
     */
    public function getPaymentStatus(string $keyId, string $keyType = 'PaymentId'): object
    {
        try {
            $url = $this->baseUrl . '/v2/GetPaymentStatus';
            
            $requestData = [
                'Key' => $keyId,
                'KeyType' => $keyType
            ];

            $response = Http::withHeaders($this->getHeaders())
                ->timeout(30)
                ->post($url, $requestData);

            if (!$response->successful()) {
                throw new Exception('MyFatoorah API request failed: ' . $response->body());
            }

            $responseData = $response->json();

            if (!$responseData['IsSuccess']) {
                $errorMessage = $responseData['Message'] ?? 'Unknown error occurred';
                throw new Exception($errorMessage);
            }

            return (object) $responseData['Data'];
        } catch (Exception $e) {
            Log::error('MyFatoorah GetPaymentStatus Error: ' . $e->getMessage(), [
                'key_id' => $keyId,
                'key_type' => $keyType,
                'api_key_prefix' => substr($this->apiKey, 0, 10) . '...',
                'base_url' => $this->baseUrl
            ]);
            throw $e;
        }
    }

    /**
     * Get available payment methods using InitiatePayment endpoint
     */
    public function getPaymentMethods(float $invoiceAmount, string $currencyIso = null): array
    {
        try {
            $url = $this->baseUrl . '/v2/InitiatePayment';
            
            $requestData = [
                'InvoiceAmount' => $invoiceAmount,
                'CurrencyIso' => $currencyIso ?? $this->getDefaultCurrency()
            ];

            $response = Http::withHeaders($this->getHeaders())
                ->timeout(30)
                ->post($url, $requestData);

            if (!$response->successful()) {
                throw new Exception('MyFatoorah API request failed: ' . $response->body());
            }

            $responseData = $response->json();

            if (!$responseData['IsSuccess']) {
                $errorMessage = $responseData['Message'] ?? 'Unknown error occurred';
                throw new Exception($errorMessage);
            }

            return $responseData['Data']['PaymentMethods'] ?? [];
        } catch (Exception $e) {
            Log::error('MyFatoorah GetPaymentMethods Error: ' . $e->getMessage(), [
                'invoice_amount' => $invoiceAmount,
                'currency_iso' => $currencyIso,
                'api_key_prefix' => substr($this->apiKey, 0, 10) . '...',
                'base_url' => $this->baseUrl
            ]);
            throw $e;
        }
    }

    /**
     * Get default currency based on country
     */
    private function getDefaultCurrency(): string
    {
        return match ($this->countryCode) {
            'SAU' => 'SAR',
            'ARE' => 'AED',
            'QAT' => 'QAR',
            'BHR' => 'BHD',
            'OMN' => 'OMR',
            'JOD' => 'JOD',
            'EGY' => 'EGP',
            'KWT' => 'KWD',
            default => 'KWD',
        };
    }

    /**
     * Format payment data for SendPayment API
     */
    public function formatPaymentData(array $orderData): array
    {
        return [
            'CustomerName' => $orderData['customer_name'] ?? '',
            'InvoiceValue' => $orderData['invoice_value'],
            'DisplayCurrencyIso' => $orderData['currency'] ?? $this->getDefaultCurrency(),
            'CustomerEmail' => $orderData['customer_email'] ?? null,
            'CallBackUrl' => $orderData['callback_url'] ?? null,
            'ErrorUrl' => $orderData['error_url'] ?? null,
            'MobileCountryCode' => $orderData['mobile_country_code'] ?? '+965',
            'CustomerMobile' => $orderData['customer_mobile'] ?? null,
            'Language' => $orderData['language'] ?? app()->getLocale(),
            'CustomerReference' => $orderData['customer_reference'] ?? null,
            'NotificationOption' => 'LNK', // Return link only
            'UserDefinedField' => $orderData['user_defined_field'] ?? null,
        ];
    }

    /**
     * Check if payment is successful
     */
    public function isPaymentSuccessful(object $paymentStatus): bool
    {
        return isset($paymentStatus->InvoiceStatus) && $paymentStatus->InvoiceStatus === 'Paid';
    }

    /**
     * Check if payment failed
     */
    public function isPaymentFailed(object $paymentStatus): bool
    {
        return isset($paymentStatus->InvoiceStatus) && 
               in_array($paymentStatus->InvoiceStatus, ['Failed', 'Expired', 'Cancelled']);
    }

    /**
     * Get payment gateway name from payment status
     */
    public function getPaymentGateway(object $paymentStatus): ?string
    {
        return $paymentStatus->focusTransaction->PaymentGateway ?? null;
    }

    /**
     * Get invoice error message
     */
    public function getInvoiceError(object $paymentStatus): ?string
    {
        return $paymentStatus->InvoiceError ?? null;
    }
}
