@extends('site.layouts.app')
@section('header-page', 'pages-body')
@section('title', trans('site.privacy_policy'))
@section('logo')
    <div class="logo">
        <a href="{{ route('home') }}"> <img src="{{  $landing_page->logo }}" alt="logo image"></a>
    </div>
@endsection
@section('content')


    <section class="breadcrumb-sec">
        <div class="container">
            <ol class="breadcrumb">
                <li class="item-home">
                    <a class="bread-link " href="{{ route('home') }}">{{ trans('site.home') }} </a>
                </li>
                <li class="active">
                    <span class="bread-current"> {{ trans('site.privacy_policy') }} </span>
                </li>
            </ol>
        </div>
    </section>
    <section class="page-terms-sec page-general-sec">
        <div class="container">
            <h3 class="page-sec-head wow  animate__fadeInDown animate__animated"> {{ trans('site.privacy_policy') }} </h3>
            @if (!is_null($privacy_policy))
                <p class="page-sec-para">
                    {!! $privacy_policy->getTranslation('description', app()->getLocale()) !!}
                </p>
            @endif
        </div>
    </section>

{{--    @include('site.includes.contacts')--}}
@endsection
