<?php

namespace Tasawk\Enum;

use Filament\Support\Contracts\HasLabel;

enum OrderStatus: string implements <PERSON><PERSON>abel {
    case PENDING_FOR_ACCEPT_ORDER = 'pending_for_accept_order';
    case PENDING_FOR_CUSTOMER_PAY = 'pending_for_customer_pay';
    case RECEIVED_AND_PREPARING = 'received_and_preparing';
    case PREPARED = 'prepared';
    case ON_WAY = 'on_way';
    case DELIVERED = 'delivered';
    case CANCELED = 'canceled';

    public function getLabel(): ?string {
        return __("panel.enums.$this->value");
    }

    public function getColor(): string {
        return match ($this->value) {
            'pending_for_accept_order', 'on_way', 'prepared' => 'warning',
            'pending_for_customer_pay', 'received_and_preparing' => 'primary',
            'delivered' => 'success',
            'canceled' => 'danger',
        };

    }

}
