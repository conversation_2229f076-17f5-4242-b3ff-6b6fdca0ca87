<?php

namespace Tasawk\Filament\Resources\Catalog\BranchResource\Pages;

use Tasawk\Filament\Resources\Catalog\BranchResource;
use Filament\Actions;
use Filament\Resources\Pages\CreateRecord;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Arr;

class CreateBranch extends CreateRecord {
    use CreateRecord\Concerns\Translatable;

    protected static string $resource = BranchResource::class;

    protected function getHeaderActions(): array {
        return [
            Actions\LocaleSwitcher::make(),
        ];
    }


}
