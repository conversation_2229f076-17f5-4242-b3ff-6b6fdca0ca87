<?php

namespace Tasawk\Models\Catalog\Branch;


use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Notification;
use Tasawk\Lib\Utils;
use Tasawk\Models\Catalog\Option;
use Tasawk\Models\Catalog\Product\ProductOption;
use Tasawk\Notifications\Branch\BranchProductOptionStatusChangedNotification;
use Tasawk\Notifications\Branch\BranchProductStatusChangedNotification;
use Tasawk\Traits\Publishable;

/**
 * @method enabledValues()
 * */
class InventoryOption extends Model {
    use Publishable;

    protected $table = 'inventories_options';
    protected $fillable = [
        'inventory_id',
        'option_id',
        'status',
        'required',
    ];

    protected static function booted() {
        parent::booted(); // TODO: Change the autogenerated stub
        static::updated(function ($option) {
            if ($option->getOriginal('status') != $option->status) {
                Notification::send(Utils::getAdministrationUsers(), new BranchProductOptionStatusChangedNotification($option->inventory, $option));
            }
        });
    }

    public function scopeWhereOriginOptionEnabled($builder) {
        return $builder->whereHas('option.option', fn($builder) => $builder->where('status', 1));
    }

    public function scopeWhereProductOptionRequired($builder) {
        return $builder->whereHas('option', fn($builder) => $builder->where('required', 1));
    }

    public function scopeWhereProductOptionStatusEnabled($builder) {
        return $builder->whereHas('option', fn($builder) => $builder->where('status', 1));
    }

    public function values() {
        return $this->hasMany(InventoryOptionValue::class, 'inventory_option_id')->whereHas("value");
    }

    public function inventory() {
        return $this->belongsTo(Inventory::class);
    }

    public function scopeEnabledValues() {
        return $this->values()->enabled();
    }

    public function option(): BelongsTo {
        return $this->belongsTo(ProductOption::class);
    }

    public function isAvailable(): bool {
        return $this->status && $this->option->option->status && $this->option->status;
    }

}
