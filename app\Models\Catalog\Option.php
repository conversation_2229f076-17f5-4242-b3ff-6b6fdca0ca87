<?php

namespace Tasawk\Models\Catalog;

use Illuminate\Database\Eloquent\SoftDeletes;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;
use Tasawk\Models\Branch;
use Tasawk\Models\Catalog\Branch\BranchValue;
use Tasawk\Models\Catalog\Branch\InventoryOption;
use Tasawk\Models\Catalog\Product\ProductOption;
use Tasawk\Traits\Publishable;
use BezhanSalleh\FilamentShield\Traits\HasPanelShield;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Spatie\Translatable\HasTranslations;

class Option extends Model implements HasMedia {
    use HasFactory, HasTranslations, Publishable,InteractsWithMedia,SoftDeletes;

    public array $translatable = ['name',];
    protected $fillable = ['name', 'type', 'status'];

    public function values(): HasMany {
        return $this->hasMany(Value::class);
    }

    public function productValues(): BelongsToMany {
        return $this->belongsToMany(Value::class, 'branch_product_option_value')
            ->withPivot("price", 'status')
            ->using(BranchValue::class);
    }

    public function products(): HasMany {
        return $this->hasMany(ProductOption::class, 'option_id');
    }

    public function inventoryOptions(): HasMany {
        return $this->hasMany(InventoryOption::class, 'option_id');
    }
}
