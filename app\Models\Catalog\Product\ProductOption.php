<?php

namespace Tasawk\Models\Catalog\Product;


use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Notification;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;
use Tasawk\Lib\Utils;
use Tasawk\Models\Catalog\Branch\InventoryOption;
use Tasawk\Models\Catalog\Option;
use Tasawk\Models\Catalog\Product;
use Tasawk\Notifications\Branch\BranchProductOptionStatusChangedNotification;
use Tasawk\Notifications\Branch\BranchProductStatusChangedNotification;
use Tasawk\Traits\Publishable;

/**
 * @method enabledValues()
 * */
class ProductOption extends Model implements HasMedia {
    use Publishable,InteractsWithMedia;

    protected $table = 'products_options';
    protected $fillable = [
        'product_id',
        'option_id',
        'status',
        'required',
    ];

    protected static function booted() {
        parent::booted(); // TODO: Change the autogenerated stub
        static::updated(function ($option) {
        });
    }

    public function scopeWhereOriginOptionEnabled($builder) {
        return $builder->whereHas('option', fn($builder) => $builder->where('status', 1));
    }

    public function values() {
        return $this->hasMany(ProductOptionValue::class, 'product_option_id');
    }

    public function product(): BelongsTo {
        return $this->belongsTo(Product::class);
    }

    public function scopeEnabledValues() {
        return $this->values()->enabled();
    }

    public function option(): BelongsTo {
        return $this->belongsTo(Option::class);
    }

    public function inventoryOptions(): HasMany {
        return $this->hasMany(InventoryOption::class, 'option_id');
    }

}
