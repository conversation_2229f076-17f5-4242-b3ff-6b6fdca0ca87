<?php

namespace Tasawk\Filament\Resources\Crm\AddressBookResource\Pages;

use Tasawk\Filament\Resources\Crm\AddressBookResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListAddressBooks extends ListRecords
{
    protected static string $resource = AddressBookResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
