<!DOCTYPE html>
<html lang="<?php echo e(app()->getLocale()); ?>" dir=<?php echo e(app()->getLocale() == 'en' ? 'ltr' : 'rtl'); ?>>

<head>
    <meta charset="UTF-8"/>
    <meta http-equiv="X-UA-Compatible" content="IE=edge"/>
    <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
    <link rel="icon" href="<?php echo e(asset("storage/".$landing_page->logo)); ?>" type="image/webp"/>
    <title><?php echo e((new \Tasawk\Settings\GeneralSettings())->app_name); ?></title>
    <?php echo $__env->make('site.includes.style', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

    <?php echo \Livewire\Mechanisms\FrontendAssets\FrontendAssets::styles(); ?>


</head>

<body data-bs-spy="scroll" data-bs-target="#navbar-example">
  <!-- preloader -->
  <div class="preloader">
    <div class="progress">
      <div class="progress-bar"></div>
    </div>
  </div>

  <?php echo $__env->make('site.includes.header', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>

  <!-- end preloader -->
<!-- end preloader -->
<!-- header -->
<!-- end of header -->

<?php echo $__env->yieldContent('content'); ?>

<?php echo $__env->make('site.includes.footer', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
<!-- to top button  -->
<button class="toTop">
    <i class="fa-light toTop-icon fa-arrow-up"></i>
</button>
  <script>
      const home_page_route= <?php echo \Illuminate\Support\Js::from(request()->route()->getName() =='home')->toHtml() ?>;
  </script>
<?php echo $__env->make('site.includes.script', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
<?php echo \Livewire\Mechanisms\FrontendAssets\FrontendAssets::scripts(); ?>


</body>

</html>
<?php /**PATH D:\Workstation\Taswk\kufa\resources\views/site/layouts/app.blade.php ENDPATH**/ ?>