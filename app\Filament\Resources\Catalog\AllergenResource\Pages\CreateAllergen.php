<?php

namespace Tasawk\Filament\Resources\Catalog\AllergenResource\Pages;

use Tasawk\Filament\Resources\Catalog\AllergenResource;
use Filament\Actions;
use Filament\Resources\Pages\CreateRecord;

class CreateAllergen extends CreateRecord {
    use CreateRecord\Concerns\Translatable;

    protected static string $resource = AllergenResource::class;

    protected function getHeaderActions(): array {
        return [
            Actions\LocaleSwitcher::make(),
        ];
    }
    protected function getRedirectUrl(): string {
        return $this->getResource()::getUrl("index");
    }
}
