<?php

namespace Tasawk\Filament\Resources\Catalog\BranchResource\Pages;

use Filament\Resources\Pages\ViewRecord;
use Tasawk\Filament\Resources\Catalog\BranchResource;
use Filament\Actions;
use Filament\Resources\Pages\CreateRecord;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Arr;

class ViewBranch extends ViewRecord {
    use ViewRecord\Concerns\Translatable;

    protected static string $resource = BranchResource::class;

    protected function getHeaderActions(): array {
        return [
            Actions\LocaleSwitcher::make(),
        ];
    }


}
