<?php

namespace Tasawk\Http\Controllers;

use Tasawk\Models\Content\CustomerReview;
use Tasawk\Models\Faq;
use Tasawk\Models\Content\Page;
use Tasawk\Settings\GeneralSettings;
use Tasawk\Settings\LandingPageSettings;
use Illuminate\Routing\Controller as BaseController;
use Illuminate\Foundation\Validation\ValidatesRequests;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;

class HomeController extends Controller {
    use AuthorizesRequests, ValidatesRequests;


    public function index() {
        $landing_page = new LandingPageSettings();
        // dd($landing_page);
        $settings = new GeneralSettings();
        $customer_reviews = CustomerReview::where('status', 1)->get();

        $google_play_link = $settings->applications_links['google_play_link'];
        $apple_store_link = $settings->applications_links['apple_store_link'];
        $faqs = Faq::where('status', 1)->take(5)->get();

        return view('site.index', compact(['landing_page', 'apple_store_link', 'google_play_link', 'settings','customer_reviews','faqs']));

    }

    public function aboutUs() {
        $settings = new GeneralSettings();
        $customer_reviews = CustomerReview::where('status', 1)->get();

        $google_play_link = $settings->applications_links['google_play_link'];
        $apple_store_link = $settings->applications_links['apple_store_link'];
        $faqs = Faq::where('status', 1)->take(5)->get();


        $landing_page = new LandingPageSettings();

        $about_us_id = $settings->app_pages['about_us'];
        $about_us = Page::findOrFail($about_us_id);

        return view('site.about-us', get_defined_vars());
    }

    public function faqs() {
        $settings = new GeneralSettings();

        $google_play_link = $settings->applications_links['google_play_link'];
        $apple_store_link = $settings->applications_links['apple_store_link'];


        $landing_page = new LandingPageSettings();
        $faqs = Faq::where('status', 1)->get();
        // dd($faqs[0]->answer);
        if (!is_null($faqs)) {
            // dd(8);
        }
        return view('site.faqs', compact(['faqs', 'landing_page', 'apple_store_link', 'google_play_link', 'settings']));
    }

    public function term() {
        $settings = new GeneralSettings();
        $customer_reviews = CustomerReview::where('status', 1)->get();

        $google_play_link = $settings->applications_links['google_play_link'];
        $apple_store_link = $settings->applications_links['apple_store_link'];
        $faqs = Faq::where('status', 1)->take(5)->get();


        $landing_page = new LandingPageSettings();
        $term_id = $settings->app_pages['terms_and_conditions'];
        $term = Page::findOrFail($term_id);

        return view('site.term', get_defined_vars());
    }

    public function privacyPolicy() {
        $settings = new GeneralSettings();
        $customer_reviews = CustomerReview::where('status', 1)->get();

        $google_play_link = $settings->applications_links['google_play_link'];
        $apple_store_link = $settings->applications_links['apple_store_link'];
        $faqs = Faq::where('status', 1)->take(5)->get();


        $landing_page = new LandingPageSettings();
        $privacy_policy_id = $settings->app_pages['privacy_policy'];
        $privacy_policy = Page::findOrFail($privacy_policy_id);
        return view('site.privacy-policy', get_defined_vars());
    }
}
