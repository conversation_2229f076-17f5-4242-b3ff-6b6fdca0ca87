<?php

namespace Tasawk\Filament\Resources\Content\CustomerReviewResource\Pages;

use Filament\Actions\LocaleSwitcher;
use Filament\Resources\Pages\CreateRecord;
use Tasawk\Filament\Resources\Content\CustomerReviewResource;

class CreateCustomerReview extends CreateRecord
{
    protected static string $resource = CustomerReviewResource::class;
    use CreateRecord\Concerns\Translatable;

    protected function getHeaderActions(): array {
        return [
            LocaleSwitcher::make(),
        ];
    }
    protected function getRedirectUrl(): string {
        return $this->getResource()::getUrl("index");
    }
}
