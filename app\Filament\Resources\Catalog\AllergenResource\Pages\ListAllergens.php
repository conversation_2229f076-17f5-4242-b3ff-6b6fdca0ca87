<?php

namespace Tasawk\Filament\Resources\Catalog\AllergenResource\Pages;

use Tasawk\Filament\Resources\Catalog\AllergenResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListAllergens extends ListRecords
{
    protected static string $resource = AllergenResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
//            Actions\LocaleSwitcher::make(),

        ];
    }
}
