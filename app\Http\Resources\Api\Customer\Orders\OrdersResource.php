<?php

namespace Tasawk\Http\Resources\Api\Customer\Orders;

use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Str;
use Tasawk\Enum\OrderStatus;
use Tasawk\Http\Resources\Api\Customer\AddressBookResource;
use Tasawk\Http\Resources\Api\Customer\Branches\BranchResource;
use Tasawk\Http\Resources\Api\Customer\Branches\LightBranchResource;
use Tasawk\Http\Resources\Api\Customer\Cart\CartProductResource;
use Tasawk\Http\Resources\Api\Customer\RateResource;
use Tasawk\Settings\GeneralSettings;

class OrdersResource extends JsonResource {

    /**
     * Transform the resource into an array.
     *
     * @param Request $request
     * @return array
     */
    public function toArray($request) {
        $cart = $this->as_cart;
        $settings = new GeneralSettings();
        return [
            "id" => $this->id,
            "status" => $this->getRenderStatus(),
            "status_code" => $this->status,
            "receipt_method" => __("panel.enums." . $this->receipt_method->value),
            "date" => $this->date?->format("Y-m-d h:i a") ?? __("Not yet determined"),
            'estimated_time' => __('panel.messages.expected_time_of_arrival', [
                'from' => $settings->min_time_to_deliver_order,
                'to' => $settings->max_time_to_deliver_order
            ]),
            "created_date" => $this->created_at->format("Y-m-d h:i a"),
            $this->mergeWhen($this->status == OrderStatus::DELIVERED, [
                'invoice_url' => route('orders.invoice.download', $this->id),
            ]),
            'customer_name' => $this->customer?->name,
            'branch' => BranchResource::make($this->branch),
            'payment' => [
                'url' => $this->payment_data['invoiceURL'] ?? null,
                'status' => __("panel.enums." . $this->payment_status->value),
                'status_code' => Str::headline($this->payment_status->value),
                'gateway' => $this->payment_data['gateway'] ?? null,
                'method' => $this->payment_data['method'] ?? null,
                'paid_at' => isset($this->payment_data['paid_at']) ? Carbon::parse($this->payment_data['paid_at'])->timezone('africa/cairo')->format('Y-m-d h:i a') : null,
            ],

            'address' => $this?->address?->name,
            'products' => CartProductResource::collection($this->as_cart->getContent()),

            $this->mergeWhen($this->cancellation()->exists(), [
                'cancellation_reason' => $this->cancellation?->reason?->name
            ]),

            $this->mergeWhen($this?->rated(), [
                'rate' => RateResource::make($this?->rate)
            ]),
            "can_rate" => $this->canRate(),
            'notes' => $this->notes,
            "totals" => $cart->formattedTotals(),

        ];
    }
}
