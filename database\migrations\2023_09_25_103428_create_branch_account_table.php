<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void {
        Schema::create('branch_account', function (Blueprint $table) {
            $table->id();
            $table->foreignId("branch_id")->constrained("branches")->cascadeOnDelete();
            $table->foreignId("account_id")->constrained("users")->cascadeOnDelete();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void {
        Schema::dropIfExists('branch_accounts');
    }
};
