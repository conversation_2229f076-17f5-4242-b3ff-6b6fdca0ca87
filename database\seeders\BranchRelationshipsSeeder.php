<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Tasawk\Models\Branch;
use Tasawk\Models\Manager;
use Tasawk\Models\Operator;

class BranchRelationshipsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get existing branches, managers, and operators
        $branches = Branch::all();
        $managers = Manager::all();
        $operators = Operator::all();

        if ($branches->isEmpty() || $managers->isEmpty() || $operators->isEmpty()) {
            $this->command->error('Please ensure you have branches, managers, and operators in the database first.');
            return;
        }

        // Clear existing relationships
        DB::table('branch_manager')->truncate();
        DB::table('branch_account')->truncate();

        // Create branch-manager relationships (one manager per branch)
        foreach ($branches->take(3) as $index => $branch) {
            if (isset($managers[$index])) {
                DB::table('branch_manager')->insert([
                    'branch_id' => $branch->id,
                    'manager_id' => $managers[$index]->id,
                ]);
                $this->command->info("Assigned manager {$managers[$index]->name} to branch {$branch->name}");
            }
        }

        // Create branch-account relationships (operators to branches)
        $operatorIndex = 0;
        foreach ($branches->take(3) as $branchIndex => $branch) {
            // Assign 2 operators to the first branch, 1 to second, 2 to third
            $operatorsPerBranch = $branchIndex == 1 ? 1 : 2;
            
            for ($i = 0; $i < $operatorsPerBranch && $operatorIndex < $operators->count(); $i++) {
                DB::table('branch_account')->insert([
                    'branch_id' => $branch->id,
                    'account_id' => $operators[$operatorIndex]->id,
                ]);
                $this->command->info("Assigned operator {$operators[$operatorIndex]->name} to branch {$branch->name}");
                $operatorIndex++;
            }
        }

        $this->command->info('Branch relationships seeded successfully!');
        $this->command->info('Branch-Manager relations: ' . DB::table('branch_manager')->count());
        $this->command->info('Branch-Account relations: ' . DB::table('branch_account')->count());
    }
}
