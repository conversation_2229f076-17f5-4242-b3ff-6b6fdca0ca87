<?php

namespace Tasawk\Notifications\Branch;

use Illuminate\Bus\Queueable;
use Illuminate\Notifications\Notification;
use Tasawk\Lib\Firebase;
use Tasawk\Lib\NotificationMessageParser;
use Tasawk\Models\Branch;
use Tasawk\Models\Catalog\Branch\Inventory;
use Tasawk\Models\Catalog\Branch\InventoryOption;
use Tasawk\Models\Catalog\Product;
use Tasawk\Models\DeviceToken;
use Tasawk\Models\Order;

class BranchProductOptionStatusChangedNotification extends Notification {
    use Queueable;

    /**
     * Create a new notification instance.
     */
    public function __construct(public Inventory $inventory, public InventoryOption $option) {
    }

    /**
     * Get the notification's delivery channels.
     *
     * @return array<int, string>
     */
    public function via(object $notifiable): array {
        return ['database'];
    }


    public function toFirebase($notifiable) {
        $tokens = DeviceToken::where('user_id', $notifiable->id)->pluck('token')->unique()->toArray();
        Firebase::make()
            ->setTokens($tokens)
            ->setTitle($this->getTitle($notifiable)[$notifiable->preferredLocale()])
            ->setBody($this->getBody($notifiable)[$notifiable->preferredLocale()])
            ->setMoreData([
                'entity_id' => $this->inventory->branch->id,
                'entity_type' => 'branch',
            ])
            ->do();

    }

    public function toArray($notifiable): array {
        $this->toFirebase($notifiable);


        return [
            'title' => json_encode($this->getTitle($notifiable)),
            'body' => json_encode($this->getBody($notifiable)),
            'format' => 'filament',
            'viewData' => [
                'entity_id' => "{$this->inventory?->branch?->id}",
                'entity_type' => 'branch',
            ],
            'duration' => 'persistent'
        ];

    }

    public function getTitle($notifiable) {
        $data = $this->data();

        return [
            'ar' => __('panel.notifications.branch_manager_change_product_option_status', [
                'branch_name' => $data['branch_name']['ar'],
                'product_name' => $data['product_name']['ar'],
                'option_name' => $data['option_name']['ar']
            ], 'ar'),
            'en' => __('panel.notifications.branch_manager_change_product_option_status', [
                'branch_name' => $data['branch_name']['en'],
                'product_name' => $data['product_name']['en'],
                'option_name' => $data['option_name']['en']
            ], 'en')
        ];

    }

    public function getBody($notifiable) {
        $data = $this->data();

        return [
            'ar' => __('panel.notifications.branch_manager_change_product_option_status_body', [
                'branch_name' => $data['branch_name']['ar'],
                'product_name' => $data['product_name']['ar'],
                'option_name' => $data['option_name']['ar'],
                'status' => $data['status']['ar'],
            ], 'ar'),
            'en' => __('panel.notifications.branch_manager_change_product_option_status_body', [
                'branch_name' => $data['branch_name']['en'],
                'product_name' => $data['product_name']['en'],
                'option_name' => $data['option_name']['en'],
                'status' => $data['status']['en'],
            ], 'en')
        ];

    }

    public function data() {
        $branch_name = [
            'ar' => $this->inventory?->branch?->getTranslation('name', 'ar'),
            'en' => $this->inventory?->branch?->getTranslation('name', 'en'),
        ];
        $product_name = [
            'ar' => $this->inventory?->product?->getTranslation('title', 'ar'),
            'en' => $this->inventory?->product?->getTranslation('title', 'en'),
        ];
        $option_name = [
            'ar' => $this->option?->option?->option?->getTranslation('name', 'ar'),
            'en' => $this->option?->option?->option?->getTranslation('name', 'en'),
        ];
        $status = [
            'ar' => $this->option->status ? __("panel.messages.activated", [], 'ar') : __("panel.messages.deactivated", [], 'ar'),
            'en' => $this->option->status ? __("panel.messages.activated", [], 'en') : __("panel.messages.deactivated", [], 'en'),
        ];
        return [
            'branch_name' => $branch_name,
            'product_name' => $product_name,
            'option_name' => $option_name,
            'status' => $status
        ];
    }
}
