<?php

namespace Tasawk\Filament\Resources\Crm;

use Filament\Tables\Filters\Filter;
use Filament\Tables\Filters\TernaryFilter;
use Tasawk\Filament\Resources\Crm\AddressBookResource\Pages;
use Tasawk\Models\AddressBook;
use Tasawk\Models\Zone;
use Tasawk\Traits\Filament\HasTranslationLabel;
use Cheesegrits\FilamentGoogleMaps\Fields\Map;
use Filament\Forms;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Toggle;
use Filament\Forms\Form;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Columns\IconColumn;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;

class AddressBookResource extends Resource {

    protected static ?string $model = AddressBook::class;
    protected static ?string $navigationIcon = 'heroicon-o-identification';
    protected static ?int $navigationSort = 2;

    public static function form(Form $form): Form {
        return $form
            ->schema([
                TextInput::make('name')
                    ->label(__("forms.fields.address_name"))
                    ->required(),

                Forms\Components\Select::make("user_id")
                    ->label(__("forms.fields.customer_name"))
                    ->relationship('customer', 'name')
                    ->required()
                ,
                Forms\Components\Select::make("zone_id")
                    ->label(__("forms.fields.zone_name"))
                    ->relationship('zone', 'name')
                    ->getOptionLabelFromRecordUsing(fn(Zone $record) => "{$record->getTranslation('name','en')} - {$record->getTranslation('name','ar')}")
                    ->required(),
                TextInput::make('state')
                    ->required(),
                TextInput::make('street')
                    ->required(),
                TextInput::make('building_number')
                    ->nullable()
                    ->required(),
                TextInput::make('floor')
                    ->nullable()
                    ->type('number'),
                TextInput::make('phone')
                    ->required()
                    ->type('number'),

                Map::make('map_location')

                    ->defaultLocation([24.7136, 46.6753])
                    ->draggable()
                    ->clickable()
                    ->geolocate()
                    ->geolocateLabel('Get Location')
                ,
                Forms\Components\Textarea::make('notes'),


                Toggle::make('primary')
                    ->default(1)
                    ->onColor('success')
                    ->offColor('danger')
            ])->columns(1);
    }

    public static function table(Table $table): Table {
        return $table
            ->columns([
                TextColumn::make('id')->searchable(),
                TextColumn::make('customer.name')->searchable(),
                TextColumn::make('name')->searchable(),
                TextColumn::make('phone')->searchable(),
                TextColumn::make('zone.name')->searchable(),
                TextColumn::make('state')->searchable(),
                TextColumn::make('street')->searchable(),
                TextColumn::make('building_number')->searchable(),
                TextColumn::make('floor')->searchable(),
                IconColumn::make('primary')->boolean()

            ])
            ->filters([
                TernaryFilter::make('primary')

            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array {
        return [
            //
        ];
    }

    public static function getPages(): array {
        return [
            'index' => Pages\ListAddressBooks::route('/'),

        ];
    }
    public static function getPluralLabel(): ?string {
        return __('menu.address_books');
    }

    public static function getLabel(): ?string {
        return __('menu.address_book');
    }
    public static function getNavigationGroup(): ?string {
        return __('menu.crm');
    }
}
