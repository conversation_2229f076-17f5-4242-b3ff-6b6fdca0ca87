<?php

namespace Tasawk\Models\Content;

use Tasawk\Models\ContactType;
use Tasawk\Models\User;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Contact extends Model {
    use HasFactory;

    protected $fillable = [
        "title",
        "message",
        "user_id",
        "name",
        "email",
        "phone",
        "seen",
        "contact_type_id",
    ];

    public function user(): BelongsTo {
        return $this->belongsTo(User::class,);
    }

    public function type(): BelongsTo {
        return $this->belongsTo(ContactType::class,'contact_type_id');
    }
}
