<?php

namespace Tasawk\Filament\Resources\Employees\OperatorResource\Pages;

use Tasawk\Filament\Resources\Employees\OperatorResource;
use Filament\Actions;
use Filament\Resources\Pages\EditRecord;

class EditOperator extends EditRecord
{
    protected static string $resource = OperatorResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\DeleteAction::make(),
        ];
    }
}
