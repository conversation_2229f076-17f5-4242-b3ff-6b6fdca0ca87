<?php

namespace Tasawk\Models\Catalog;

use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Spatie\MediaLibrary\HasMedia;
use <PERSON><PERSON>\MediaLibrary\InteractsWithMedia;
use Tasawk\Traits\Publishable;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Spatie\Translatable\HasTranslations;
use Tasawk\Traits\Searchable;

class Allergen extends Model implements HasMedia {
    use HasFactory, HasTranslations,Publishable,InteractsWithMedia,Searchable;

    public array $translatable = ['title','description'];

    protected $fillable = ['title','description','status'];

    public function products(): BelongsToMany {
        return $this->belongsToMany(Product::class);

    }

}
