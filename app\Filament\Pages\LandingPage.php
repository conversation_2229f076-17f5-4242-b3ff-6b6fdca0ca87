<?php

namespace Tasawk\Filament\Pages;

use Filament\Forms;
use Filament\Forms\Form;
use Filament\Pages\Page;
use Filament\Pages\SettingsPage;
use Illuminate\Support\Facades\DB;
use Filament\Forms\Components\Tabs;
use Filament\Forms\Components\Radio;
use Tasawk\Settings\GeneralSettings;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Repeater;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\TextInput;
use Filament\Notifications\Notification;
use Tasawk\Settings\LandingPageSettings;
use Filament\Forms\Components\FileUpload;
use Illuminate\Contracts\Support\Htmlable;
use Tasawk\Forms\Components\SelectFontAwesomeIcon;
use Filament\Forms\Components\SpatieMediaLibraryFileUpload;

class LandingPage extends SettingsPage
{


    protected static ?string $navigationIcon = 'heroicon-o-document-text';

    protected static ?int $navigationSort = 2;
    protected static string $settings = LandingPageSettings::class;
    protected static ?string $slug = 'settings/landing-page';



    public function form(Form $form): Form
    {
        return $form
            ->schema([
                FileUpload::make('logo')
                    ->columnSpanFull()
                    ->required(),




                    Section::make("basic_info")
                    ->label(__("sections.basic_info"))
                    ->schema([

                        Tabs::make('Label')
                            ->tabs([
                                Tabs\Tab::make(__('panel.languages.arabic'))
                                    ->schema([
                                        TextInput::make('bas_info.title.ar')
                                            ->label(__('forms.fields.title'))
                                            ->extraAttributes(['x-model' => 'about_description_ar'])
                                            ->required(),
                                        TextInput::make('bas_info.description.ar')
                                            ->label(__('forms.fields.description'))
                                            ->extraAttributes(['x-model' => 'about_description_ar'])
                                            ->required(),
                                    ]),
                                Tabs\Tab::make(__('panel.languages.english'))
                                    ->schema([
                                        TextInput::make('bas_info.title.en')
                                            ->label(__('forms.fields.title'))
                                            ->extraAttributes(['x-model' => 'about_description_ar'])
                                            ->required(),
                                        TextInput::make('bas_info.description.en')
                                            ->label(__('forms.fields.description'))
                                            ->extraAttributes(['x-model' => 'about_description_ar'])
                                            ->required(),
                                    ]),]),

                                    FileUpload::make('bas_info.image')
                                    ->label(__('forms.fields.image'))
                                    ->required(),

                    ]),



                Section::make("about_us")
                    ->label(__("sections.about_us"))
                    ->schema([
                        Radio::make('about_description.active')
                            ->label(__('forms.fields.active'))

                            ->boolean()
                            ->options([
                                '0' => 'لا',
                                '1' => 'نعم',
                            ])
                            ->inline()
                            ->inlineLabel(false),
                        Tabs::make('Label')
                            ->tabs([
                                Tabs\Tab::make(__('panel.languages.arabic'))
                                    ->schema([
                                        TextInput::make('about_description.ar')
                                            ->label(__('forms.fields.description'))
                                            ->extraAttributes(['x-model' => 'about_description_ar'])
                                            ->required(),
                                    ]),
                                Tabs\Tab::make(__('panel.languages.english'))
                                    ->schema([
                                        TextInput::make('about_description.en')
                                            ->label(__('forms.fields.description'))
                                            ->extraAttributes(['x-model' => 'about_description_en'])
                                            ->required(),
                                    ]),
                            ]),
                        Repeater::make("about_features")
                            ->label(__("sections.about_features"))
                            ->schema([
                                Tabs::make('Label')
                                    ->tabs([
                                        Tabs\Tab::make(__('panel.languages.arabic'))
                                            ->schema([
                                                TextInput::make('title.ar')
                                                    ->label(__('forms.fields.title'))
                                                    ->required(),
                                            ]),
                                        Tabs\Tab::make(__('panel.languages.english'))
                                            ->schema([
                                                TextInput::make('title.en')
                                                    ->label(__('forms.fields.title'))
                                                    ->required(),
                                            ]),
                                    ]),
                                SelectFontAwesomeIcon::make('icon')
                                     ->setMode('all')
                                    ->searchable()
                                    ->required()
                                    ->allowHtml(),
                            ])->defaultItems(1),
                    ]),
                Section::make("our_features")
                    ->label(__("sections.our_features"))
                    ->schema([
                        Radio::make('our_features_description.active')
                            ->label(__('forms.fields.active'))

                            ->boolean()
                            ->options([
                                '0' => 'لا',
                                '1' => 'نعم',
                            ])
                            ->inline()
                            ->inlineLabel(false),

                        Tabs::make('Label')
                            ->tabs([
                                Tabs\Tab::make(__('panel.languages.arabic'))
                                    ->schema([
                                        TextArea::make('our_features_description.ar')
                                            ->label(__('forms.fields.description'))
                                            ->required(),
                                    ]),
                                Tabs\Tab::make(__('panel.languages.english'))
                                    ->schema([
                                        TextArea::make('our_features_description.en')
                                            ->label(__('forms.fields.description'))
                                            ->required(),
                                    ]),
                            ]),
                        Repeater::make("features")
                            ->label(__("sections.about_features"))
                            ->schema([
                                Tabs::make('Label')
                                    ->tabs([
                                        Tabs\Tab::make(__('panel.languages.arabic'))
                                            ->schema([
                                                TextInput::make('title.ar')
                                                    ->label(__('forms.fields.title'))
                                                    ->required(),
                                                SelectFontAwesomeIcon::make('icon')
                                                     ->setMode('all')
                                                    ->searchable()
                                                    ->required()
                                                    ->allowHtml(),
                                            ]),
                                        Tabs\Tab::make(__('panel.languages.english'))
                                            ->schema([
                                                TextInput::make('title.en')
                                                    ->label(__('forms.fields.title'))
                                                    ->required(),
                                                SelectFontAwesomeIcon::make('icon')
                                                     ->setMode('all')
                                                    ->searchable()
                                                    ->required()
                                                    ->allowHtml(),
                                            ]),
                                    ]),
                            ])->defaultItems(1),
                            FileUpload::make('our_features_image')
                            ->label(__('forms.fields.image'))
                            ->required(),
                        // FileUpload::make('feature_image')
                        //     ->label(__('forms.fields.image'))
                        //     ->required(),
                    ]),
                Section::make("app_screen")
                    ->label(__("sections.app_screen"))
                    ->schema([
                        Radio::make('app_screen.active')
                            ->label(__('forms.fields.active'))

                            ->boolean()
                            ->options([
                                '0' => 'لا',
                                '1' => 'نعم',
                            ])
                            ->inline()
                            ->inlineLabel(false),
                        Repeater::make("app_screen.image")
                            ->label(__("sections.app_screen"))
                            ->schema([
                                FileUpload::make('image')
                                    ->label(__('forms.fields.image'))
                                    ->required(),
                                // FileUpload::make('image')
                                //     ->label(__('forms.fields.image'))
                                //     ->required(),
                            ])->defaultItems(1),
                    ]),


                Section::make("clients_reviews")
                    ->label(__("sections.clients_reviews"))
                    ->schema([
                        Radio::make('clients_reviews.active')
                            ->label(__('forms.fields.active'))

                            ->boolean()
                            ->options([
                                '0' => 'لا',
                                '1' => 'نعم',
                            ])
                            ->inline()
                            ->inlineLabel(false),

                    ]),


                Section::make("faq")
                    ->label(__("sections.faq"))
                    ->schema([
                        Radio::make('faq.active')
                            ->label(__('forms.fields.active'))

                            ->boolean()
                            ->options([
                                '0' => 'لا',
                                '1' => 'نعم',
                            ])
                            ->inline()
                            ->inlineLabel(false),
                    ]),


                Section::make("contact_us")
                    ->label(__("sections.contact_us"))
                    ->schema([
                        Radio::make('contact_us.active')
                            ->label(__('forms.fields.active'))

                            ->boolean()
                            ->options([
                                '0' => 'لا',
                                '1' => 'نعم',
                            ])
                            ->inline()
                            ->inlineLabel(false),
                    ])

            ]);
    }



    public static function getNavigationLabel(): string
    {
        return __("menu.landing_page");
    }

    public function getHeading(): string|Htmlable
    {
        return __('menu.landing_page');
    }

    public static function getNavigationGroup(): ?string
    {
        return __('menu.settings');
    }
    public function getTitle(): string|Htmlable
    {
        return __('menu.landing_page');
    }
}
