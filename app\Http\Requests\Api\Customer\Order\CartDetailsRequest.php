<?php

namespace Tasawk\Http\Requests\Api\Customer\Order;

use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;
use Tasawk\Lib\Utils;
use Tasawk\Rules\AddressBelongToAuthUserRule;
use Tasawk\Rules\IsProductAvailableInBranchRule;
use Tasawk\Rules\IsRequiredProductOptionsRepresentRule;
use Tasawk\Rules\IsValidProductOptionsRule;
use Tasawk\Rules\IsValidProductOptionValuesRule;


class CartDetailsRequest extends FormRequest {
    protected $stopOnFirstFailure = true;

    public function authorize() {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules(): array {
        return [
            "receipt_method" => ['required', Rule::in(Utils::getBranchReceiptMethods())],
            'payment_gateway' => ['required', 'in:myfatoorah,cash'],
            "address_id" => [Rule::requiredIf($this->get('receipt_method') == 'delivery'), new AddressBelongToAuthUserRule],

            'products' => ['required', 'array'],
            'products.*.id' => ['required', new IsProductAvailableInBranchRule(), new IsRequiredProductOptionsRepresentRule()],
            'products.*.quantity' => ['required', 'numeric', 'min:1'],
            'products.*.options.*.id' => [new IsValidProductOptionsRule()],
            'products.*.options.*.value_id' => [new IsValidProductOptionValuesRule()],
            'notes' => ['nullable']
        ];
    }

}
