<?php

namespace Tasawk\Actions\Shared\Order;

use App\Models\User;
use Str;
use Tasawk\Coupons\Models\Coupons;
use Tasawk\Employees\Models\Contractor;
use Tasawk\Orders\Models\Order;
use Tasawk\Orders\Models\Order\Timeline;
use Tasawk\Orders\Models\OrderStatuses;
use Tasawk\Orders\Notifications\NewOrderNotification;
use Tasawk\Utilities\Lib\ActionWithData;

class CheckoutTutorialOrderAction extends ActionWithData
{
    protected $data = [];

    public function __construct($cart)
    {
        $order = Order::create([
            "uuid" => Str::uuid(),
            "status" => OrderStatuses::COMPLETED,
            'user_id' => auth()->id(),
            'total' => $cart->getTotal(),
            "date" => now()->toDateTime(),
            "contractor_id" => User::first()?->id,
            'order_type' => 'purchase_tutorials'
        ]);
        $this->data = $order;
        $order->addTimeLine(Timeline::EVENT_CREATED);
    }

    function data()
    {
        return $this->data;
    }
}
