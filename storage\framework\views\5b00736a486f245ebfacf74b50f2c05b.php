 <?php if($landing_page->contact_us['active'] ==1): ?>
 <section class="contact" id="contact-sec">
    <div class="container">
      <div class="titles-parent">
        <h4 class="sub-title  "><?php echo e(trans('site.contact_us')); ?></h4></h4>
        <h2 class="general-title" data-aos="fade-down" data-aos-easing=" ease-in" data-aos-duration="1000">
        <?php echo e(trans('site.happy_to_contact_you')); ?>

        </h2>
      </div>
      <div class="contact-content">
        <div class="form-div ">
          <p class="text">
            <?php echo e(trans('site.send_your_message')); ?>

          </p>
          <?php
$__split = function ($name, $params = []) {
    return [$name, $params];
};
[$__name, $__params] = $__split('contact-us', []);

$__html = app('livewire')->mount($__name, $__params, 'lw-3661706075-0', $__slots ?? [], get_defined_vars());

echo $__html;

unset($__html);
unset($__name);
unset($__params);
unset($__split);
if (isset($__slots)) unset($__slots);
?>
        </div>
        <div class="social-div ">
          <h3 class="head"><?php echo e(trans('site.contact_info')); ?></h3></h3>
          <div class="social-items">
            <div class="item">
              <div class="d-flex text-icon">
                <i class="fa-regular icon fa-location-dot"></i>
                <span class="text"><?php echo e(trans('site.address')); ?></span>
              </div>
              <a  class="item-val">
                <?php echo e($settings->app_address); ?>

              </a>
            </div>
            <div class="item">
              <div class="d-flex text-icon">
                <i class="fa-regular icon fa-mobile"></i>
                <span class="text"> <?php echo e(trans('site.phone_app')); ?></span>
              </div>
              <a href="tel:<?php echo e($settings->app_phone); ?>" class="item-val">
              <?php echo e($settings->app_phone); ?>

              </a>
            </div>
            <div class="item">
              <div class="d-flex text-icon">
                <i class="fa-regular icon  fa-envelope"></i>
                <span class="text"> <?php echo e(trans('site.app_email')); ?></span>
              </div>
              <a href="mailto:<?php echo e($settings->app_email); ?>" class="item-val">
              <?php echo e($settings->app_email); ?>

              </a>
            </div>



          </div>

          <div class="social-icons">
                    <?php $__currentLoopData = $settings->social_links; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $media): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <a href="<?php echo e($media['link']); ?>">
                            <i class="fa-brands fa-<?php echo e($media['icon']); ?>"></i>
                        </a>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </div>
        </div>
      </div>
    </div>
  </section>
 <?php endif; ?>
<?php /**PATH D:\Workstation\Taswk\kufa\resources\views/site/includes/contacts.blade.php ENDPATH**/ ?>