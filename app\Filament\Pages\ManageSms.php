<?php

namespace Tasawk\Filament\Pages;

use <PERSON><PERSON>han<PERSON>alleh\FilamentShield\Traits\HasPageShield;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Tabs;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Form;
use Filament\Pages\SettingsPage;
use Illuminate\Contracts\Support\Htmlable;
use Tasawk\Settings\SMSConfig;

class ManageSms extends SettingsPage {
    use HasPageShield;

    protected static ?int $navigationSort = 6;
    protected static ?string $navigationIcon = 'heroicon-o-at-symbol';

    protected static string $settings = SMSConfig::class;

    public function form(Form $form): Form {
        return $form
            ->schema([
                Section::make('SMS')
                    ->schema([
                        Tabs::make('Label')
                            ->tabs([
                                Tabs\Tab::make(__('panel.languages.arabic'))
                                    ->schema([
                                        Textarea::make('message_on_order_created.ar')
                                            ->label(__('forms.fields.message_on_order_created'))
                                            ->rows(5)
                                            ->required(),
                                        Textarea::make('message_on_order_on_the_way.ar')
                                            ->label(__('forms.fields.message_on_order_on_the_way'))
                                            ->required()
                                            ->rows(5)
                                            ->translateLabel(),

                                        Textarea::make('message_on_order_is_delivered.ar')
                                            ->label(__('forms.fields.message_on_order_is_delivered'))
                                            ->required()
                                            ->rows(5)
                                            ->translateLabel(),
                                        Textarea::make('message_on_receipt_from_branch.ar')
                                            ->label(__('forms.fields.message_on_receipt_from_branch'))
                                            ->required()
                                            ->rows(5)
                                            ->translateLabel(),

                                    ]),
                                Tabs\Tab::make(__('panel.languages.english'))
                                    ->schema([
                                        Textarea::make('message_on_order_created.en')
                                            ->label(__('forms.fields.message_on_order_created'))
                                            ->required(),
                                        Textarea::make('message_on_order_on_the_way.en')
                                            ->label(__('forms.fields.message_on_order_on_the_way'))
                                            ->required()
                                            ->rows(5)
                                            ->translateLabel(),

                                        Textarea::make('message_on_order_is_delivered.en')
                                            ->label(__('forms.fields.message_on_order_is_delivered'))
                                            ->required()
                                            ->rows(5)
                                            ->translateLabel(),
                                        Textarea::make('message_on_receipt_from_branch.en')
                                            ->label(__('forms.fields.message_on_receipt_from_branch'))
                                            ->required()
                                            ->rows(5)
                                            ->translateLabel(),
                                    ]),
                            ]),


                    ])]);
    }

    public function getHeading(): string|Htmlable {
        return __('sections.sms_management');
    }

    public static function getNavigationLabel(): string {
        return __("menu.manage_sms");
    }

    public static function getNavigationGroup(): ?string {
        return __('menu.settings');
    }


    public function getTitle(): string|Htmlable {
        return __("menu.manage_sms");
    }
    public function getBreadcrumbs(): array {
        return [
            null =>static::getNavigationGroup(),
            static::getUrl() => static::getNavigationLabel(),
        ];
    }
}
