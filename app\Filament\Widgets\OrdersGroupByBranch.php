<?php

namespace Tasawk\Filament\Widgets;

use <PERSON><PERSON><PERSON><PERSON><PERSON>h\FilamentShield\Traits\HasWidgetShield;
use Filament\Widgets\ChartWidget;
use Illuminate\Contracts\Support\Htmlable;
use Tasawk\Models\Branch;

class OrdersGroupByBranch extends ChartWidget {
    use HasWidgetShield;
    protected static ?string $heading = 'Chart';
    protected static ?int $sort = 4;
    protected function getData(): array {
        $stats = Branch::withCount('orders')->get()->mapWithKeys(fn(Branch $branch) => [$branch->name => $branch->orders_count]);

        return [
            'datasets' => [
                [
                    'label' => 'Blog posts created',
                    'data' => $stats->values(),
                    'backgroundColor' => [
                        '#ff9999',
                        '#66b3ff',
                        '#ffcc99'
                    ],
                ],
            ],
            'labels' => $stats->keys(),
        ];
    }

    protected function getType(): string {
        return 'doughnut';
    }

    public function getHeading(): string|Htmlable|null {
        return __('panel.stats.orders_group_by_branch');
    }

    public function getDescription(): string|Htmlable|null {
        return __('panel.stats.orders_group_by_branch_description');
    }
    public function getTableHeading(): ?string {
        return $this->getHeading();
    }
}
