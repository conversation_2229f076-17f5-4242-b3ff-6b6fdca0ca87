<?php

namespace Tasawk\Models\Catalog\Branch;

use Cknow\Money\Casts\MoneyDecimalCast;
use Illuminate\Database\Eloquent\Model;
use Tasawk\Models\Catalog\Product\ProductOptionValue;
use Tasawk\Models\Catalog\Value;
use Tasawk\Traits\Publishable;

class InventoryOptionValue extends Model {
    use Publishable;

    protected $table = 'inventories_options_values';
    protected $fillable = [
        'inventory_option_id',
        'value_id',
        'status',

    ];

    protected $casts = [
        'price' => MoneyDecimalCast::class
    ];

    public function option() {
        return $this->belongsTo(InventoryOption::class, 'inventory_option_id');
    }

    public function value() {
        return $this->belongsTo(ProductOptionValue::class);
    }
    public function isAvailable() {

        return $this->status  && $this->value->status;

    }

}
