<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    /**
     * Run the migrations.
     */
    public function up(): void {
        Schema::create('branch_manager', function (Blueprint $table) {
            $table->id();
            $table->foreignId("branch_id")->constrained("branches")->cascadeOnDelete();
            $table->foreignId("manager_id")->constrained("users")->cascadeOnDelete();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void {
        Schema::dropIfExists('branch_managers');
    }
};
