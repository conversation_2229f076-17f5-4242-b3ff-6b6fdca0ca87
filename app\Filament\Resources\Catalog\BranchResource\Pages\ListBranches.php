<?php

namespace Tasawk\Filament\Resources\Catalog\BranchResource\Pages;

use Tasawk\Filament\Resources\Catalog\BranchResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListBranches extends ListRecords
{
    protected static string $resource = BranchResource::class;

    protected function getHeaderActions(): array
    {
        return [
            Actions\CreateAction::make(),
        ];
    }
}
