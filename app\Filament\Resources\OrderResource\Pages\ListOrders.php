<?php

namespace Tasawk\Filament\Resources\OrderResource\Pages;

use Arr;
use Filament\Pages\Concerns\ExposesTableToWidgets;
use Filament\Resources\Pages\ListRecords;
use Filament\Resources\Pages\ListRecords\Tab;
use Illuminate\Database\Eloquent\Builder;
use Tasawk\Enum\OrderStatus;
use Tasawk\Filament\Resources\OrderResource;
use Tasawk\Filament\Resources\OrderResource\Widgets\OrderStats;
use Tasawk\Models\Order;

class ListOrders extends ListRecords {
    use ExposesTableToWidgets;

    protected static string $resource = OrderResource::class;

    protected function getHeaderActions(): array {
        return [
//            Actions\CreateAction::make(),

        ];
    }

    public function getTabs(): array {
        $tabs = [];
        foreach (OrderStatus::cases() as $case) {


            $tabs[__("panel.enums.$case->value")] = Tab::make()
                ->badge(Order::query()->when(Arr::get($this->tableFilters, 'today_orders.isActive', 'true') == "true", fn($builder) => $builder->today())->where('status', $case->value)->count())
                ->badgeColor($case->getColor())
                ->badgeColor($case->getColor())
                ->modifyQueryUsing(fn(Builder $query) => $query->where('status', $case->value));
        }
        return [
            __('panel.enums.all') => Tab::make()->badge(Order::query()->when(Arr::get($this->tableFilters, 'today_orders.isActive', 'true') == "true", fn($builder) => $builder->today())->count()),
            ...$tabs
        ];
    }

    public function getDefaultActiveTab(): string|int|null {
        return __('panel.enums.all');
    }

    public function getHeaderWidgets(): array {
        return [
            OrderStats::class,
//            \Tasawk\Filament\Widgets\OrdersChart::class
        ];
    }
}
