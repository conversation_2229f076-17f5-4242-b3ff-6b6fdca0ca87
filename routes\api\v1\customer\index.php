<?php


use Tasawk\Api\V1\Customer\BranchServices;
use Tasawk\Api\V1\Customer\CartServices;
use Tasawk\Api\V1\Customer\ProductServices;
use Tasawk\Http\Middleware\EnsureThatBranchPresentInRequestHeader;

Route::prefix('v1')->group(function () {
    require_once __DIR__ . '/auth.php';
    require_once __DIR__ . '/profile.php';

    Route::get('branches', [BranchServices::class, 'index']);
    Route::post('branches/closest/address', [BranchServices::class, 'getClosestBasedOnAddress'])->middleware('auth:sanctum');
    Route::post('branches/closest/coordinate', [BranchServices::class, 'getClosestBasedOnCoordinate']);

    Route::get('products', [ProductServices::class, 'index']);
    Route::get('products/{product}', [ProductServices::class, 'show']);
    Route::post('products/{product}/favorite', [ProductServices::class, 'toggleFavorite'])->middleware('auth:sanctum');

    Route::group(['middleware' => EnsureThatBranchPresentInRequestHeader::class], function () {

        Route::post('cart/details',[CartServices::class,'details'])->middleware('auth:sanctum');
        Route::post('cart/checkout',[CartServices::class,'checkout'])->middleware('auth:sanctum');
    });
});
