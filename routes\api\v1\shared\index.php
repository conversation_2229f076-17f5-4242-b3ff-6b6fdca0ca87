<?php

use Tasawk\Api\V1\Customer\ProductServices;
use Tasawk\Api\V1\Shared\BannerServices;
use Tasawk\Api\V1\Shared\CategoryServices;
use Tasawk\Api\V1\Shared\ContentServices;
use Tasawk\Api\V1\Shared\LocationServices;
use Tasawk\Api\V1\Shared\NotificationServices;
use Tasawk\Api\V1\Shared\SharedProfileService;
use Tasawk\Http\Middleware\EnsureThatBranchPresentInRequestHeader;

Route::prefix('v1')->group(function () {
    include __DIR__ . '/settings.php';

    Route::get('banners', [BannerServices::class, 'list']);
    Route::get('categories', [CategoryServices::class, 'list']);
    Route::get('search', [ProductServices::class, 'search']);

    Route::get('categories/{category}', [CategoryServices::class, 'show']);


    Route::post('contacts', [ContentServices::class, 'contact']);
    Route::get('pages/{slug}', [ContentServices::class, 'page']);
    Route::get('locations/zones', [LocationServices::class, 'zones']);


    Route::middleware('auth:sanctum')->group(function () {
        Route::post('users/device-token', [SharedProfileService::class, 'updateDeviceToken']);
        Route::delete('users/delete-account', [SharedProfileService::class, 'deleteAccount']);

        Route::get('users/notifications', [NotificationServices::class, 'all']);
        Route::delete('users/notifications/{id?}', [NotificationServices::class, 'destroy']);
        Route::post('users/notifications/fcm', [NotificationServices::class, 'fcm']);
    });

});
