<?php

namespace Tasawk\Api\V1\Shared;

use Notification;
use Tasawk\Api\Facade\Api;
use Tasawk\Http\Resources\Api\Customer\NotificationResource;
use Tasawk\Lib\Firebase;
use Tasawk\Models\DeviceToken;
use Tasawk\Notifications\SendAdminMessagesNotification;


class NotificationServices {
    public function all() {
        $notifications = NotificationResource::collection(auth()->user()->notifications()->latest()->paginate());
        return Api::isOk(__("Notification list"))->setData($notifications);
    }

    public function destroy($notification = null) {
        !$notification
            ? auth()->user()->notifications()->delete()
            : auth()->user()->notifications()->where("id", $notification)->delete();
        return Api::isOk(__("Notification has been deleted"));
    }

    public function fcm() {
        $title= request()->get('title');
        $body = request()->get('body');
        $notifiable = auth()->user();
        $tokens = DeviceToken::where('user_id', $notifiable->id)->pluck('token')->unique()->toArray();
        return Firebase::make()
            ->setTokens($tokens)
            ->setTitle($title[$notifiable->preferredLocale()])
            ->setBody($body[$notifiable->preferredLocale()])
            ->do();
    }

}
