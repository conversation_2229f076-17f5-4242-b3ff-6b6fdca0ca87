<?php

namespace Tasawk\Filament\Resources\OrderResource\Widgets;

use BezhanSalleh\FilamentShield\Traits\HasWidgetShield;
use Cknow\Money\Money;
use DB;
use Filament\Widgets\Concerns\InteractsWithPageTable;
use Filament\Widgets\StatsOverviewWidget as BaseWidget;
use Filament\Widgets\StatsOverviewWidget\Stat;
use Tasawk\Enum\OrderStatus;
use Tasawk\Filament\Resources\OrderResource\Pages\ListOrders;

class OrderStats extends BaseWidget {
    use HasWidgetShield;
    use InteractsWithPageTable;
    protected static ?int $sort = 1;
    protected static function getPermissionName(): string {
        return 'view_any_order';
    }

    protected function getStats(): array {
        $stats = DB::table('orders')->select(
            DB::raw("SUM(
		CASE WHEN status = 'pending_for_accept_order'
		THEN
			total
		ELSE
			0
		END) AS 'new',
		SUM(
		CASE WHEN status in( 'received_and_preparing','prepared','on_way' )
		THEN
			total
		ELSE
			0
		END) AS 'in_processing',
		SUM(
		CASE WHEN status = 'delivered'
		THEN
			total
		ELSE
			0
		END) AS 'delivered',

			SUM(
		CASE WHEN status = 'canceled'
		THEN
			total
		ELSE
			0
		END) AS 'canceled'
"))->first();
//        Money::setLocale(session()->get('locale'));
        return [

            Stat::make(__('panel.stats.new_orders_total'), Money::parse($this->getPageTableQuery()->where('status', OrderStatus::PENDING_FOR_ACCEPT_ORDER)->sum('total'))->format()),
            Stat::make(__('panel.stats.in_processing_orders_total'), Money::parse($this->getPageTableQuery()->whereIn('status', [OrderStatus::PENDING_FOR_CUSTOMER_PAY, OrderStatus::PREPARED, OrderStatus::RECEIVED_AND_PREPARING, OrderStatus::ON_WAY])->sum('total'))->format()),
            Stat::make(__('panel.stats.completed_orders_total'), Money::parse($this->getPageTableQuery()->where('status', OrderStatus::DELIVERED)->sum('total'))->format()),
            Stat::make(__('panel.stats.canceled_orders_total'), Money::parse($this->getPageTableQuery()->where('status', OrderStatus::CANCELED)->sum('total'))->format()),
        ];
    }


    protected function getTablePage(): string {
        return ListOrders::class;
    }
}
