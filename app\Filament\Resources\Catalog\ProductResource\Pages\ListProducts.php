<?php

namespace Tasawk\Filament\Resources\Catalog\ProductResource\Pages;

use Tasawk\Filament\Resources\Catalog\ProductResource;
use Filament\Actions;
use Filament\Resources\Pages\ListRecords;

class ListProducts extends ListRecords {

    protected static string $resource = ProductResource::class;

    protected function getHeaderActions(): array {
        return [
            Actions\CreateAction::make(),
//            Actions\LocaleSwitcher::make(),
        ];
    }
}
